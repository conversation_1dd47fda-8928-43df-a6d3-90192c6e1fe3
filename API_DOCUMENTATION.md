# PropBolt API Key Management Documentation

## Overview

PropBolt provides API key management functionality for users with `data` account types to access property data programmatically. This system includes API key creation, usage tracking, rate limiting, and comprehensive analytics.

## Authentication

### Account Types
- **`land`**: Internal admin access to land search dashboard
- **`data`**: Customer API access for property data endpoints
- **`NULL`**: No access (prevents login)

### API Key Authentication
All data API endpoints require authentication via API key in the Authorization header:

```
Authorization: Bearer YOUR_API_KEY
```

Or as a raw API key:
```
Authorization: YOUR_API_KEY
```

## API Key Management Endpoints

### 1. List User API Keys
**GET** `/api/v1/user/api-keys`

Returns all API keys for the authenticated user (masked for security).

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "user_id": 123,
      "key_name": "Production API Key",
      "api_key": "abcd...xyz9",
      "is_active": true,
      "rate_limit": 1000,
      "created_at": "2024-01-15T10:30:00Z",
      "last_used_at": "2024-01-15T14:22:00Z"
    }
  ]
}
```

### 2. Create New API Key
**POST** `/api/v1/user/api-keys/create`

Creates a new API key for the authenticated user.

**Request Body:**
```json
{
  "key_name": "My API Key",
  "rate_limit": 1000
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 2,
    "user_id": 123,
    "key_name": "My API Key",
    "api_key": "1234567890abcdef1234567890abcdef12345678",
    "is_active": true,
    "rate_limit": 1000,
    "created_at": "2024-01-15T15:30:00Z"
  },
  "message": "API key created successfully"
}
```

### 3. Delete API Key
**DELETE** `/api/v1/user/api-keys/delete`

Deletes an API key owned by the authenticated user.

**Request Body:**
```json
{
  "key_id": 2
}
```

**Response:**
```json
{
  "success": true,
  "message": "API key deleted successfully"
}
```

### 4. Get API Key Usage Statistics
**GET** `/api/v1/user/api-keys/usage?api_key_id=1`

Returns detailed usage statistics for a specific API key.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "api_key_id": 1,
      "endpoint": "/api/v1/data/search",
      "request_count": 45,
      "date": "2024-01-15T00:00:00Z",
      "created_at": "2024-01-15T10:30:00Z"
    }
  ]
}
```

### 5. Get API Key Aggregated Statistics
**GET** `/api/v1/user/api-keys/stats?api_key_id=1`

Returns aggregated statistics and analytics for an API key.

**Response:**
```json
{
  "success": true,
  "data": {
    "total_requests": 1250,
    "endpoint_breakdown": {
      "/api/v1/data/search": 800,
      "/api/v1/data/property": 350,
      "/api/v1/data/search/sold": 100
    },
    "daily_usage": {
      "2024-01-15": 45,
      "2024-01-14": 67,
      "2024-01-13": 23
    },
    "rate_limit_usage": 0.045,
    "api_key": "abcd...xyz9",
    "key_name": "Production API Key",
    "is_active": true,
    "rate_limit": 1000
  }
}
```

## Data API Endpoints (Require API Key)

### 1. Property Details
**GET** `/api/v1/data/property?id=123`

Get detailed information about a specific property.

**Parameters:**
- `id` (required): Property ID

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 123,
    "address": "123 Main St, Daytona Beach, FL",
    "price": 150000,
    "size": "0.25 acres",
    "zoning": "Residential",
    "latitude": 29.2108,
    "longitude": -81.0228,
    "description": "Vacant land ready for development",
    "habitability": "Buildable",
    "proximity": "0.5 miles to beach",
    "chain_lease_potential": "High"
  }
}
```

### 2. Property Search
**GET** `/api/v1/data/search`

Search for properties with various filters using RealEstateAPI.com.

**Parameters:**
- `city` (optional): City to search
- `state` (optional): State to search
- `county` (optional): County to search
- `zip` (optional): ZIP code to search
- `property_type` (optional): Property type (default: "LAND")
- `vacant` (optional): Search for vacant properties (true/false)
- `min_price` (optional): Minimum property value
- `max_price` (optional): Maximum property value
- `min_lot_size` (optional): Minimum lot size in square feet
- `max_lot_size` (optional): Maximum lot size in square feet

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "property_id",
      "address": "123 Main St",
      "city": "Daytona Beach",
      "state": "FL",
      "zipCode": "32114",
      "latitude": 29.2108,
      "longitude": -81.0228,
      "propertyType": "LAND",
      "value": 150000,
      "lotSize": 10890,
      "vacant": true
    }
  ],
  "count": 25,
  "pagination": {
    "currentPage": 1,
    "totalPages": 5,
    "pageSize": 25,
    "totalResults": 125,
    "hasMore": true
  }
}
```

### 3. Sold Properties Search
**GET** `/api/v1/data/search/sold?city=Daytona Beach&state=FL`

Search for land properties with sale history in a specific location.

**Parameters:**
- `city` (required): City to search
- `state` (required): State to search

### 4. Rental Properties Search
**GET** `/api/v1/data/search/rentals?city=Daytona Beach&state=FL`

Search for vacant land properties that could potentially be rented.

**Parameters:**
- `city` (required): City to search
- `state` (required): State to search

### 5. Property Details (Enhanced)
**GET** `/api/v1/data/property/details?address=123 Main St, Daytona Beach, FL`

Get comprehensive property information using RealEstateAPI.com.

**Parameters:**
- `address` (optional): Full property address
- `id` (optional): Property ID
- `city` (optional): City name
- `state` (optional): State abbreviation
- `zip` (optional): ZIP code

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "property_id",
    "address": {
      "fullAddress": "123 Main St, Daytona Beach, FL 32114",
      "city": "Daytona Beach",
      "state": "FL",
      "zipCode": "32114"
    },
    "propertyInfo": {
      "bedrooms": 0,
      "bathrooms": 0,
      "propertyType": "LAND",
      "yearBuilt": null
    },
    "ownerInfo": {
      "name": "John Doe",
      "mailingAddress": "456 Oak St, Orlando, FL 32801",
      "absenteeOwner": true
    },
    "taxInfo": {
      "assessedValue": 145000,
      "landValue": 145000,
      "taxAmount": 2900
    },
    "saleHistory": [
      {
        "saleDate": "2020-05-15",
        "salePrice": 120000,
        "saleType": "Warranty Deed"
      }
    ]
  }
}
```

### 6. AutoComplete
**GET** `/api/v1/data/autocomplete?search=Daytona`

Get address autocomplete suggestions using RealEstateAPI.com.

**Parameters:**
- `search` (required): Search query (minimum 3 characters)
- `search_types` (optional): Comma-separated list of search types (A=Address, C=City, etc.)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "title": "Daytona Beach, FL",
      "city": "Daytona Beach",
      "state": "FL",
      "searchType": "C"
    }
  ],
  "count": 1
}
```

### 7. Health Check
**GET** `/api/v1/data/health`

Check API health status.

## Rate Limiting

- Default rate limit: 1000 requests per day
- Rate limits are enforced per API key
- Rate limit headers are included in responses:
  - `X-RateLimit-Limit`: Your daily limit
  - `X-RateLimit-Remaining`: Remaining requests for today

## Error Responses

### 401 Unauthorized
```json
{
  "error": "API key required"
}
```

### 429 Too Many Requests
```json
{
  "error": "Rate limit exceeded",
  "message": "You have exceeded your daily API request quota"
}
```

### 404 Not Found
```json
{
  "error": "Property not found"
}
```

## Usage Examples

### cURL Examples

**Create API Key:**
```bash
curl -X POST https://propbolt.com/api/v1/user/api-keys/create \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_SESSION_TOKEN" \
  -d '{"key_name": "My API Key", "rate_limit": 1000}'
```

**Search Properties:**
```bash
curl -X GET "https://propbolt.com/api/v1/data/search?location=Daytona Beach, FL&min_price=50000&max_price=200000" \
  -H "Authorization: Bearer YOUR_API_KEY"
```

### JavaScript Example

```javascript
const apiKey = 'your_api_key_here';
const response = await fetch('https://propbolt.com/api/v1/data/search?location=Daytona Beach, FL', {
  headers: {
    'Authorization': `Bearer ${apiKey}`,
    'Content-Type': 'application/json'
  }
});
const data = await response.json();
console.log(data);
```

## Database Schema

### api_keys Table
- `id`: Primary key
- `user_id`: Foreign key to users table
- `key_name`: User-friendly name
- `api_key`: 64-character hex string
- `is_active`: Boolean status
- `rate_limit`: Daily request limit
- `created_at`: Creation timestamp
- `last_used_at`: Last usage timestamp
- `expires_at`: Optional expiration date

### api_usage Table
- `id`: Primary key
- `api_key_id`: Foreign key to api_keys table
- `endpoint`: API endpoint accessed
- `request_count`: Number of requests
- `date`: Date of usage
- `created_at`: Creation timestamp

## Security Notes

- API keys are generated using cryptographically secure random bytes
- Keys are stored as plain text for validation (consider hashing in production)
- Rate limiting prevents abuse
- Usage tracking enables monitoring and analytics
- Keys can be deactivated without deletion for audit purposes
