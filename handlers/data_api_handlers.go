package handlers

import (
    "database/sql"
    "encoding/json"
    "net/http"
    "strconv"
    "strings"

    "propbolt/database"
    "propbolt/realestate"
)

// PropertyHandler handles property details requests via API key authentication
func PropertyHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodGet {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    // Get property ID from query parameter
    propertyIDStr := r.URL.Query().Get("id")
    if propertyIDStr == "" {
        http.Error(w, "Property ID is required", http.StatusBadRequest)
        return
    }

    propertyID, err := strconv.Atoi(propertyIDStr)
    if err != nil {
        http.Error(w, "Invalid property ID", http.StatusBadRequest)
        return
    }

    // Get property details from database
    property, err := getPropertyByID(propertyID)
    if err != nil {
        if err == sql.ErrNoRows {
            http.Error(w, "Property not found", http.StatusNotFound)
            return
        }
        http.Error(w, "Failed to retrieve property: "+err.Error(), http.StatusInternalServerError)
        return
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "success": true,
        "data":    property,
    })
}

// SearchHandler handles property search requests via API key authentication
func SearchHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodGet {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    // Create RealEstateAPI client
    client := realestate.NewClient()

    // Parse ALL available search parameters

    // Location parameters
    city := r.URL.Query().Get("city")
    state := r.URL.Query().Get("state")
    county := r.URL.Query().Get("county")
    zip := r.URL.Query().Get("zip")
    address := r.URL.Query().Get("address")

    var latitude, longitude, radius float64
    if latStr := r.URL.Query().Get("latitude"); latStr != "" {
        latitude, _ = strconv.ParseFloat(latStr, 64)
    }
    if lngStr := r.URL.Query().Get("longitude"); lngStr != "" {
        longitude, _ = strconv.ParseFloat(lngStr, 64)
    }
    if radiusStr := r.URL.Query().Get("radius"); radiusStr != "" {
        radius, _ = strconv.ParseFloat(radiusStr, 64)
    }

    // Property type
    propertyType := r.URL.Query().Get("property_type")
    if propertyType == "" {
        propertyType = "LAND"
    }

    // Property details
    var bedsMin, bedsMax, bathsMin, bathsMax int
    var buildingSizeMin, buildingSizeMax, lotSizeMin, lotSizeMax int
    var yearBuiltMin, yearBuiltMax, unitsMin, unitsMax int

    if val := r.URL.Query().Get("beds_min"); val != "" {
        bedsMin, _ = strconv.Atoi(val)
    }
    if val := r.URL.Query().Get("beds_max"); val != "" {
        bedsMax, _ = strconv.Atoi(val)
    }
    if val := r.URL.Query().Get("baths_min"); val != "" {
        bathsMin, _ = strconv.Atoi(val)
    }
    if val := r.URL.Query().Get("baths_max"); val != "" {
        bathsMax, _ = strconv.Atoi(val)
    }
    if val := r.URL.Query().Get("building_size_min"); val != "" {
        buildingSizeMin, _ = strconv.Atoi(val)
    }
    if val := r.URL.Query().Get("building_size_max"); val != "" {
        buildingSizeMax, _ = strconv.Atoi(val)
    }
    if val := r.URL.Query().Get("lot_size_min"); val != "" {
        lotSizeMin, _ = strconv.Atoi(val)
    }
    if val := r.URL.Query().Get("lot_size_max"); val != "" {
        lotSizeMax, _ = strconv.Atoi(val)
    }
    if val := r.URL.Query().Get("year_built_min"); val != "" {
        yearBuiltMin, _ = strconv.Atoi(val)
    }
    if val := r.URL.Query().Get("year_built_max"); val != "" {
        yearBuiltMax, _ = strconv.Atoi(val)
    }
    if val := r.URL.Query().Get("units_min"); val != "" {
        unitsMin, _ = strconv.Atoi(val)
    }
    if val := r.URL.Query().Get("units_max"); val != "" {
        unitsMax, _ = strconv.Atoi(val)
    }

    // Financial filters
    var valueMin, valueMax, equityMin, equityMax int
    var salePriceMin, salePriceMax int
    saleDateMin := r.URL.Query().Get("sale_date_min")
    saleDateMax := r.URL.Query().Get("sale_date_max")

    if val := r.URL.Query().Get("value_min"); val != "" {
        valueMin, _ = strconv.Atoi(val)
    }
    if val := r.URL.Query().Get("value_max"); val != "" {
        valueMax, _ = strconv.Atoi(val)
    }
    if val := r.URL.Query().Get("equity_min"); val != "" {
        equityMin, _ = strconv.Atoi(val)
    }
    if val := r.URL.Query().Get("equity_max"); val != "" {
        equityMax, _ = strconv.Atoi(val)
    }
    if val := r.URL.Query().Get("sale_price_min"); val != "" {
        salePriceMin, _ = strconv.Atoi(val)
    }
    if val := r.URL.Query().Get("sale_price_max"); val != "" {
        salePriceMax, _ = strconv.Atoi(val)
    }

    // MLS parameters
    mlsActive := r.URL.Query().Get("mls_active") == "true"
    mlsPending := r.URL.Query().Get("mls_pending") == "true"
    mlsSold := r.URL.Query().Get("mls_sold") == "true"

    var mlsDaysOnMarketMin, mlsDaysOnMarketMax int
    if val := r.URL.Query().Get("mls_days_on_market_min"); val != "" {
        mlsDaysOnMarketMin, _ = strconv.Atoi(val)
    }
    if val := r.URL.Query().Get("mls_days_on_market_max"); val != "" {
        mlsDaysOnMarketMax, _ = strconv.Atoi(val)
    }

    // Ownership filters
    absenteeOwner := r.URL.Query().Get("absentee_owner") == "true"
    corporateOwned := r.URL.Query().Get("corporate_owned") == "true"
    trustOwned := r.URL.Query().Get("trust_owned") == "true"
    llcOwned := r.URL.Query().Get("llc_owned") == "true"
    ownerOccupied := r.URL.Query().Get("owner_occupied") == "true"

    var ownershipLengthMin, ownershipLengthMax int
    if val := r.URL.Query().Get("ownership_length_min"); val != "" {
        ownershipLengthMin, _ = strconv.Atoi(val)
    }
    if val := r.URL.Query().Get("ownership_length_max"); val != "" {
        ownershipLengthMax, _ = strconv.Atoi(val)
    }

    // Property condition
    vacant := r.URL.Query().Get("vacant") == "true"
    distressed := r.URL.Query().Get("distressed") == "true"
    foreclosure := r.URL.Query().Get("foreclosure") == "true"
    preForeclosure := r.URL.Query().Get("pre_foreclosure") == "true"
    auction := r.URL.Query().Get("auction") == "true"
    taxLien := r.URL.Query().Get("tax_lien") == "true"
    codeViolation := r.URL.Query().Get("code_violation") == "true"
    floodZone := r.URL.Query().Get("flood_zone")

    // Investment filters
    highEquity := r.URL.Query().Get("high_equity") == "true"
    negativeEquity := r.URL.Query().Get("negative_equity") == "true"
    freeClear := r.URL.Query().Get("free_clear") == "true"
    cashBuyer := r.URL.Query().Get("cash_buyer") == "true"
    assumableLoan := r.URL.Query().Get("assumable_loan") == "true"
    reo := r.URL.Query().Get("reo") == "true"
    quitClaim := r.URL.Query().Get("quit_claim") == "true"

    var flippedMinTimes, flippedWithinYears int
    if val := r.URL.Query().Get("flipped_min_times"); val != "" {
        flippedMinTimes, _ = strconv.Atoi(val)
    }
    if val := r.URL.Query().Get("flipped_within_years"); val != "" {
        flippedWithinYears, _ = strconv.Atoi(val)
    }

    // Construction/features
    constructionType := r.URL.Query().Get("construction_type")
    heatingType := r.URL.Query().Get("heating_type")
    coolingType := r.URL.Query().Get("cooling_type")
    pool := r.URL.Query().Get("pool") == "true"
    garage := r.URL.Query().Get("garage") == "true"
    basement := r.URL.Query().Get("basement") == "true"
    waterfront := r.URL.Query().Get("waterfront") == "true"

    // Multi-family
    mfh2to4 := r.URL.Query().Get("mfh_2to4") == "true"

    // Special modes
    count := r.URL.Query().Get("count") == "true"
    summary := r.URL.Query().Get("summary") == "true"
    idsOnly := r.URL.Query().Get("ids_only") == "true"

    var exclude []string
    if excludeStr := r.URL.Query().Get("exclude"); excludeStr != "" {
        exclude = strings.Split(excludeStr, ",")
    }

    // Pagination and sorting
    var resultIndex, size int
    if val := r.URL.Query().Get("result_index"); val != "" {
        resultIndex, _ = strconv.Atoi(val)
    }
    if val := r.URL.Query().Get("size"); val != "" {
        size, _ = strconv.Atoi(val)
    }
    if size == 0 {
        size = 100 // Default size
    }

    userID := r.URL.Query().Get("user_id")

    // Parse sort parameters
    var sort map[string]string
    if sortStr := r.URL.Query().Get("sort"); sortStr != "" {
        sort = make(map[string]string)
        sortPairs := strings.Split(sortStr, ",")
        for _, pair := range sortPairs {
            if parts := strings.Split(pair, ":"); len(parts) == 2 {
                sort[parts[0]] = parts[1]
            }
        }
    }

    // Build comprehensive search request with ALL parameters
    searchReq := &realestate.PropertySearchRequest{
        // Location parameters
        Address:   address,
        City:      city,
        State:     state,
        County:    county,
        Zip:       zip,
        Latitude:  latitude,
        Longitude: longitude,
        Radius:    radius,

        // Property details
        PropertyType:     propertyType,
        BedsMin:          bedsMin,
        BedsMax:          bedsMax,
        BathsMin:         bathsMin,
        BathsMax:         bathsMax,
        BuildingSizeMin:  buildingSizeMin,
        BuildingSizeMax:  buildingSizeMax,
        LotSizeMin:       lotSizeMin,
        LotSizeMax:       lotSizeMax,
        YearBuiltMin:     yearBuiltMin,
        YearBuiltMax:     yearBuiltMax,
        UnitsMin:         unitsMin,
        UnitsMax:         unitsMax,

        // Financial filters
        ValueMin:     valueMin,
        ValueMax:     valueMax,
        EquityMin:    equityMin,
        EquityMax:    equityMax,
        SalePriceMin: salePriceMin,
        SalePriceMax: salePriceMax,
        SaleDateMin:  saleDateMin,
        SaleDateMax:  saleDateMax,

        // MLS parameters - Default to FOR SALE ONLY unless specified
        MLSActive:             mlsActive || (!mlsActive && !mlsPending && !mlsSold), // Default to active if none specified
        MLSPending:            mlsPending,
        MLSSold:               mlsSold,
        MLSDaysOnMarketMin:    mlsDaysOnMarketMin,
        MLSDaysOnMarketMax:    mlsDaysOnMarketMax,

        // Ownership filters
        AbsenteeOwner:      absenteeOwner,
        CorporateOwned:     corporateOwned,
        TrustOwned:         trustOwned,
        LLCOwned:           llcOwned,
        OwnerOccupied:      ownerOccupied,
        OwnershipLengthMin: ownershipLengthMin,
        OwnershipLengthMax: ownershipLengthMax,

        // Property condition
        Vacant:         vacant,
        Distressed:     distressed,
        Foreclosure:    foreclosure,
        PreForeclosure: preForeclosure,
        Auction:        auction,
        TaxLien:        taxLien,
        CodeViolation:  codeViolation,
        FloodZone:      floodZone,

        // Investment filters
        HighEquity:         highEquity,
        NegativeEquity:     negativeEquity,
        FreeClear:          freeClear,
        CashBuyer:          cashBuyer,
        AssumableLoan:      assumableLoan,
        REO:                reo,
        QuitClaim:          quitClaim,
        FlippedMinTimes:    flippedMinTimes,
        FlippedWithinYears: flippedWithinYears,

        // Construction/features
        ConstructionType: constructionType,
        HeatingType:      heatingType,
        CoolingType:      coolingType,
        Pool:             pool,
        Garage:           garage,
        Basement:         basement,
        Waterfront:       waterfront,

        // Multi-family
        MFH2to4: mfh2to4,

        // Special modes
        Count:   count,
        Summary: summary,
        IDsOnly: idsOnly,
        Exclude: exclude,

        // Pagination and sorting
        Sort:        sort,
        ResultIndex: resultIndex,
        Size:        size,
        UserID:      userID,
    }

    // Search properties using RealEstateAPI
    results, err := client.PropertySearch(searchReq)
    if err != nil {
        http.Error(w, "Failed to search properties: "+err.Error(), http.StatusInternalServerError)
        return
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "success": true,
        "data":    results.Results,
        "count":   results.ResultCount,
        "pagination": results.Pagination,
    })
}

// SoldSearchHandler handles sold property search requests via API key authentication
func SoldSearchHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodGet {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    // Create RealEstateAPI client
    client := realestate.NewClient()

    // Parse search parameters
    city := r.URL.Query().Get("city")
    state := r.URL.Query().Get("state")

    if city == "" || state == "" {
        http.Error(w, "City and state parameters are required", http.StatusBadRequest)
        return
    }

    // Search for land properties with sale history - SOLD PROPERTIES ONLY
    searchReq := &realestate.PropertySearchRequest{
        Size:         100,
        City:         city,
        State:        state,
        PropertyType: "LAND",
        Vacant:       true,
        // SOLD PROPERTIES ONLY
        MLSActive:    false,
        MLSPending:   false,
        MLSSold:      true,
    }

    results, err := client.PropertySearch(searchReq)
    if err != nil {
        http.Error(w, "Failed to search sold properties: "+err.Error(), http.StatusInternalServerError)
        return
    }

    // For each property, get detailed info including sale history
    var soldProperties []map[string]interface{}
    for _, property := range results.Results {
        detailReq := &realestate.PropertyDetailRequest{
            ID: property.ID,
        }

        details, err := client.PropertyDetail(detailReq)
        if err != nil {
            continue // Skip properties that can't be detailed
        }

        // Check if property has sale history
        if len(details.SaleHistory) > 0 {
            soldProperties = append(soldProperties, map[string]interface{}{
                "id":          property.ID,
                "address":     property.Address,
                "city":        property.City,
                "state":       property.State,
                "value":       property.Value,
                "latitude":    property.Latitude,
                "longitude":   property.Longitude,
                "saleHistory": details.SaleHistory,
                "propertyInfo": details.PropertyInfo,
            })
        }
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "success": true,
        "data":    soldProperties,
        "count":   len(soldProperties),
    })
}

// RentalsSearchHandler handles rental property search requests via API key authentication
func RentalsSearchHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodGet {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    // Create RealEstateAPI client
    client := realestate.NewClient()

    // Parse search parameters
    city := r.URL.Query().Get("city")
    state := r.URL.Query().Get("state")

    if city == "" || state == "" {
        http.Error(w, "City and state parameters are required", http.StatusBadRequest)
        return
    }

    // Note: RealEstateAPI doesn't have a specific rental search for land
    // This would typically be done through MLS Search API for rental listings
    // For now, we'll return vacant land FOR SALE that could potentially be rented
    searchReq := &realestate.PropertySearchRequest{
        Size:         100,
        City:         city,
        State:        state,
        PropertyType: "LAND",
        Vacant:       true,
        // FOR SALE ONLY - Active MLS listings
        MLSActive:    true,
        MLSPending:   false,
        MLSSold:      false,
    }

    results, err := client.PropertySearch(searchReq)
    if err != nil {
        http.Error(w, "Failed to search rental properties: "+err.Error(), http.StatusInternalServerError)
        return
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "success": true,
        "data":    results.Results,
        "count":   results.ResultCount,
        "message": "Showing vacant land properties that could potentially be rented",
    })
}

// Helper function to get property by ID from database
func getPropertyByID(propertyID int) (*database.Property, error) {
    query := `SELECT id, address, price, size, zoning, latitude, longitude, description,
              habitability, proximity, chain_lease_potential, days_on_market, price_per_sqft,
              created_at, updated_at FROM properties WHERE id = $1`

    var property database.Property
    err := database.DB.QueryRow(query, propertyID).Scan(
        &property.ID, &property.Address, &property.Price, &property.Size, &property.Zoning,
        &property.Latitude, &property.Longitude, &property.Description, &property.Habitability,
        &property.Proximity, &property.ChainLeasePotential, &property.DaysOnMarket,
        &property.PricePerSqFt, &property.CreatedAt, &property.UpdatedAt,
    )

    if err != nil {
        return nil, err
    }

    return &property, nil
}

// PropertyDetailsHandler handles detailed property information requests
func PropertyDetailsHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodGet {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    // Create RealEstateAPI client
    client := realestate.NewClient()

    // Get address or ID from query parameters
    address := r.URL.Query().Get("address")
    id := r.URL.Query().Get("id")
    city := r.URL.Query().Get("city")
    state := r.URL.Query().Get("state")
    zip := r.URL.Query().Get("zip")

    if address == "" && id == "" && (city == "" || state == "") {
        http.Error(w, "Address, ID, or city/state parameters are required", http.StatusBadRequest)
        return
    }

    // Build detail request
    detailReq := &realestate.PropertyDetailRequest{
        Address: address,
        ID:      id,
        City:    city,
        State:   state,
        Zip:     zip,
    }

    // Get property details using RealEstateAPI
    propertyDetails, err := client.PropertyDetail(detailReq)
    if err != nil {
        if strings.Contains(err.Error(), "not found") {
            http.Error(w, "Property not found", http.StatusNotFound)
            return
        }
        http.Error(w, "Failed to retrieve property details: "+err.Error(), http.StatusInternalServerError)
        return
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "success": true,
        "data":    propertyDetails,
    })
}

// AutoCompleteHandler handles address autocomplete requests via API key authentication
func AutoCompleteHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodGet {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    // Create RealEstateAPI client
    client := realestate.NewClient()

    // Get search query from parameter
    search := r.URL.Query().Get("search")
    if search == "" {
        http.Error(w, "Search parameter is required", http.StatusBadRequest)
        return
    }

    // Get search types from parameter (optional)
    searchTypesParam := r.URL.Query().Get("search_types")
    var searchTypes []string
    if searchTypesParam != "" {
        searchTypes = strings.Split(searchTypesParam, ",")
    }

    // Get autocomplete results using RealEstateAPI
    results, err := client.AutoComplete(search, searchTypes)
    if err != nil {
        http.Error(w, "Failed to get autocomplete results: "+err.Error(), http.StatusInternalServerError)
        return
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "success": true,
        "data":    results.Results,
        "count":   len(results.Results),
    })
}

// HealthCheckHandler provides a health check endpoint for API monitoring
func HealthCheckHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodGet {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "success": true,
        "status":  "healthy",
        "message": "PropBolt API is running",
    })
}
