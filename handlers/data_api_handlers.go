package handlers

import (
    "database/sql"
    "encoding/json"
    "net/http"
    "strconv"
    "strings"

    "propbolt/database"
    "propbolt/realestate"
)

// PropertyHandler handles property details requests via API key authentication
func PropertyHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodGet {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    // Get property ID from query parameter
    propertyIDStr := r.URL.Query().Get("id")
    if propertyIDStr == "" {
        http.Error(w, "Property ID is required", http.StatusBadRequest)
        return
    }

    propertyID, err := strconv.Atoi(propertyIDStr)
    if err != nil {
        http.Error(w, "Invalid property ID", http.StatusBadRequest)
        return
    }

    // Get property details from database
    property, err := getPropertyByID(propertyID)
    if err != nil {
        if err == sql.ErrNoRows {
            http.Error(w, "Property not found", http.StatusNotFound)
            return
        }
        http.Error(w, "Failed to retrieve property: "+err.Error(), http.StatusInternalServerError)
        return
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "success": true,
        "data":    property,
    })
}

// SearchHandler handles property search requests via API key authentication
func SearchHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodGet {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    // Create RealEstateAPI client
    client := realestate.NewClient()

    // Parse search parameters
    city := r.URL.Query().Get("city")
    state := r.URL.Query().Get("state")
    county := r.URL.Query().Get("county")
    zip := r.URL.Query().Get("zip")

    // Default to vacant land search if no specific type provided
    propertyType := r.URL.Query().Get("property_type")
    if propertyType == "" {
        propertyType = "LAND"
    }

    vacant := r.URL.Query().Get("vacant") == "true"

    var valueMin, valueMax, lotSizeMin, lotSizeMax int

    if minPriceStr := r.URL.Query().Get("min_price"); minPriceStr != "" {
        valueMin, _ = strconv.Atoi(minPriceStr)
    }

    if maxPriceStr := r.URL.Query().Get("max_price"); maxPriceStr != "" {
        valueMax, _ = strconv.Atoi(maxPriceStr)
    }

    if minLotSizeStr := r.URL.Query().Get("min_lot_size"); minLotSizeStr != "" {
        lotSizeMin, _ = strconv.Atoi(minLotSizeStr)
    }

    if maxLotSizeStr := r.URL.Query().Get("max_lot_size"); maxLotSizeStr != "" {
        lotSizeMax, _ = strconv.Atoi(maxLotSizeStr)
    }

    // Build search request
    searchReq := &realestate.PropertySearchRequest{
        Size:         100,
        City:         city,
        State:        state,
        County:       county,
        Zip:          zip,
        PropertyType: propertyType,
        Vacant:       vacant,
        ValueMin:     valueMin,
        ValueMax:     valueMax,
        LotSizeMin:   lotSizeMin,
        LotSizeMax:   lotSizeMax,
    }

    // Search properties using RealEstateAPI
    results, err := client.PropertySearch(searchReq)
    if err != nil {
        http.Error(w, "Failed to search properties: "+err.Error(), http.StatusInternalServerError)
        return
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "success": true,
        "data":    results.Results,
        "count":   results.ResultCount,
        "pagination": results.Pagination,
    })
}

// SoldSearchHandler handles sold property search requests via API key authentication
func SoldSearchHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodGet {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    // Create RealEstateAPI client
    client := realestate.NewClient()

    // Parse search parameters
    city := r.URL.Query().Get("city")
    state := r.URL.Query().Get("state")

    if city == "" || state == "" {
        http.Error(w, "City and state parameters are required", http.StatusBadRequest)
        return
    }

    // Search for land properties with sale history
    searchReq := &realestate.PropertySearchRequest{
        Size:         100,
        City:         city,
        State:        state,
        PropertyType: "LAND",
        Vacant:       true,
    }

    results, err := client.PropertySearch(searchReq)
    if err != nil {
        http.Error(w, "Failed to search sold properties: "+err.Error(), http.StatusInternalServerError)
        return
    }

    // For each property, get detailed info including sale history
    var soldProperties []map[string]interface{}
    for _, property := range results.Results {
        detailReq := &realestate.PropertyDetailRequest{
            ID: property.ID,
        }

        details, err := client.PropertyDetail(detailReq)
        if err != nil {
            continue // Skip properties that can't be detailed
        }

        // Check if property has sale history
        if len(details.SaleHistory) > 0 {
            soldProperties = append(soldProperties, map[string]interface{}{
                "id":          property.ID,
                "address":     property.Address,
                "city":        property.City,
                "state":       property.State,
                "value":       property.Value,
                "latitude":    property.Latitude,
                "longitude":   property.Longitude,
                "saleHistory": details.SaleHistory,
                "propertyInfo": details.PropertyInfo,
            })
        }
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "success": true,
        "data":    soldProperties,
        "count":   len(soldProperties),
    })
}

// RentalsSearchHandler handles rental property search requests via API key authentication
func RentalsSearchHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodGet {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    // Create RealEstateAPI client
    client := realestate.NewClient()

    // Parse search parameters
    city := r.URL.Query().Get("city")
    state := r.URL.Query().Get("state")

    if city == "" || state == "" {
        http.Error(w, "City and state parameters are required", http.StatusBadRequest)
        return
    }

    // Note: RealEstateAPI doesn't have a specific rental search for land
    // This would typically be done through MLS Search API for rental listings
    // For now, we'll return vacant land that could potentially be rented
    searchReq := &realestate.PropertySearchRequest{
        Size:         100,
        City:         city,
        State:        state,
        PropertyType: "LAND",
        Vacant:       true,
    }

    results, err := client.PropertySearch(searchReq)
    if err != nil {
        http.Error(w, "Failed to search rental properties: "+err.Error(), http.StatusInternalServerError)
        return
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "success": true,
        "data":    results.Results,
        "count":   results.ResultCount,
        "message": "Showing vacant land properties that could potentially be rented",
    })
}

// Helper function to get property by ID from database
func getPropertyByID(propertyID int) (*database.Property, error) {
    query := `SELECT id, address, price, size, zoning, latitude, longitude, description,
              habitability, proximity, chain_lease_potential, days_on_market, price_per_sqft,
              created_at, updated_at FROM properties WHERE id = $1`

    var property database.Property
    err := database.DB.QueryRow(query, propertyID).Scan(
        &property.ID, &property.Address, &property.Price, &property.Size, &property.Zoning,
        &property.Latitude, &property.Longitude, &property.Description, &property.Habitability,
        &property.Proximity, &property.ChainLeasePotential, &property.DaysOnMarket,
        &property.PricePerSqFt, &property.CreatedAt, &property.UpdatedAt,
    )

    if err != nil {
        return nil, err
    }

    return &property, nil
}

// PropertyDetailsHandler handles detailed property information requests
func PropertyDetailsHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodGet {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    // Create RealEstateAPI client
    client := realestate.NewClient()

    // Get address or ID from query parameters
    address := r.URL.Query().Get("address")
    id := r.URL.Query().Get("id")
    city := r.URL.Query().Get("city")
    state := r.URL.Query().Get("state")
    zip := r.URL.Query().Get("zip")

    if address == "" && id == "" && (city == "" || state == "") {
        http.Error(w, "Address, ID, or city/state parameters are required", http.StatusBadRequest)
        return
    }

    // Build detail request
    detailReq := &realestate.PropertyDetailRequest{
        Address: address,
        ID:      id,
        City:    city,
        State:   state,
        Zip:     zip,
    }

    // Get property details using RealEstateAPI
    propertyDetails, err := client.PropertyDetail(detailReq)
    if err != nil {
        if strings.Contains(err.Error(), "not found") {
            http.Error(w, "Property not found", http.StatusNotFound)
            return
        }
        http.Error(w, "Failed to retrieve property details: "+err.Error(), http.StatusInternalServerError)
        return
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "success": true,
        "data":    propertyDetails,
    })
}

// AutoCompleteHandler handles address autocomplete requests via API key authentication
func AutoCompleteHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodGet {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    // Create RealEstateAPI client
    client := realestate.NewClient()

    // Get search query from parameter
    search := r.URL.Query().Get("search")
    if search == "" {
        http.Error(w, "Search parameter is required", http.StatusBadRequest)
        return
    }

    // Get search types from parameter (optional)
    searchTypesParam := r.URL.Query().Get("search_types")
    var searchTypes []string
    if searchTypesParam != "" {
        searchTypes = strings.Split(searchTypesParam, ",")
    }

    // Get autocomplete results using RealEstateAPI
    results, err := client.AutoComplete(search, searchTypes)
    if err != nil {
        http.Error(w, "Failed to get autocomplete results: "+err.Error(), http.StatusInternalServerError)
        return
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "success": true,
        "data":    results.Results,
        "count":   len(results.Results),
    })
}

// HealthCheckHandler provides a health check endpoint for API monitoring
func HealthCheckHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodGet {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "success": true,
        "status":  "healthy",
        "message": "PropBolt API is running",
    })
}
