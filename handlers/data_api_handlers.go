package handlers

import (
    "database/sql"
    "encoding/json"
    "net/http"
    "strconv"
    "strings"

    "propbolt/database"
    "propbolt/search"
    "propbolt/details"
)

// PropertyHandler handles property details requests via API key authentication
func PropertyHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodGet {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    // Get property ID from query parameter
    propertyIDStr := r.URL.Query().Get("id")
    if propertyIDStr == "" {
        http.Error(w, "Property ID is required", http.StatusBadRequest)
        return
    }

    propertyID, err := strconv.Atoi(propertyIDStr)
    if err != nil {
        http.Error(w, "Invalid property ID", http.StatusBadRequest)
        return
    }

    // Get property details from database
    property, err := getPropertyByID(propertyID)
    if err != nil {
        if err == sql.ErrNoRows {
            http.Error(w, "Property not found", http.StatusNotFound)
            return
        }
        http.Error(w, "Failed to retrieve property: "+err.Error(), http.StatusInternalServerError)
        return
    }

    w.<PERSON>er().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "success": true,
        "data":    property,
    })
}

// SearchHandler handles property search requests via API key authentication
func SearchHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodGet {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    // Parse search parameters
    filters := make(map[string]interface{})
    
    if location := r.URL.Query().Get("location"); location != "" {
        filters["location"] = location
    }
    
    if minPriceStr := r.URL.Query().Get("min_price"); minPriceStr != "" {
        if minPrice, err := strconv.Atoi(minPriceStr); err == nil {
            filters["minPrice"] = minPrice
        }
    }
    
    if maxPriceStr := r.URL.Query().Get("max_price"); maxPriceStr != "" {
        if maxPrice, err := strconv.Atoi(maxPriceStr); err == nil {
            filters["maxPrice"] = maxPrice
        }
    }
    
    if zoning := r.URL.Query().Get("zoning"); zoning != "" {
        filters["zoning"] = zoning
    }
    
    if chainPotential := r.URL.Query().Get("chain_potential"); chainPotential != "" {
        filters["chainPotential"] = chainPotential
    }

    // Get properties from database
    properties, err := database.GetProperties(filters)
    if err != nil {
        http.Error(w, "Failed to search properties: "+err.Error(), http.StatusInternalServerError)
        return
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "success": true,
        "data":    properties,
        "count":   len(properties),
    })
}

// SoldSearchHandler handles sold property search requests via API key authentication
func SoldSearchHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodGet {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    // Parse search parameters - using basic geographic bounds for now
    // In a real implementation, you would geocode the location to get bounds
    neLat := 29.3
    neLong := -80.9
    swLat := 29.1
    swLong := -81.1

    pagination := 1
    zoom := 12

    // Use the search package to get sold properties
    results, mapResults, err := search.Sold(pagination, zoom, neLat, neLong, swLat, swLong, nil)
    if err != nil {
        http.Error(w, "Failed to search sold properties: "+err.Error(), http.StatusInternalServerError)
        return
    }

    // Convert results to a more API-friendly format
    apiResults := make([]map[string]interface{}, len(results))
    for i, result := range results {
        apiResults[i] = map[string]interface{}{
            "zpid":        result.Zpid,
            "address":     result.Address,
            "price":       result.Price,
            "bedrooms":    result.Bedrooms,
            "bathrooms":   result.Bathrooms,
            "livingArea":  result.LivingArea,
            "lotAreaValue": result.LotAreaValue,
            "lotAreaUnit":  result.LotAreaUnit,
            "latitude":    result.Latitude,
            "longitude":   result.Longitude,
        }
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "success": true,
        "data":    apiResults,
        "count":   len(apiResults),
        "mapData": mapResults,
    })
}

// RentalsSearchHandler handles rental property search requests via API key authentication
func RentalsSearchHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodGet {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    // Parse search parameters - using basic geographic bounds for now
    // In a real implementation, you would geocode the location to get bounds
    neLat := 29.3
    neLong := -80.9
    swLat := 29.1
    swLong := -81.1

    pagination := 1
    zoom := 12

    // Use the search package to get rental properties
    results, mapResults, err := search.ForRent(pagination, zoom, neLat, neLong, swLat, swLong, nil)
    if err != nil {
        http.Error(w, "Failed to search rental properties: "+err.Error(), http.StatusInternalServerError)
        return
    }

    // Convert results to a more API-friendly format
    apiResults := make([]map[string]interface{}, len(results))
    for i, result := range results {
        apiResults[i] = map[string]interface{}{
            "zpid":        result.Zpid,
            "address":     result.Address,
            "price":       result.Price,
            "bedrooms":    result.Bedrooms,
            "bathrooms":   result.Bathrooms,
            "livingArea":  result.LivingArea,
            "lotAreaValue": result.LotAreaValue,
            "lotAreaUnit":  result.LotAreaUnit,
            "latitude":    result.Latitude,
            "longitude":   result.Longitude,
        }
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "success": true,
        "data":    apiResults,
        "count":   len(apiResults),
        "mapData": mapResults,
    })
}

// Helper function to get property by ID from database
func getPropertyByID(propertyID int) (*database.Property, error) {
    query := `SELECT id, address, price, size, zoning, latitude, longitude, description,
              habitability, proximity, chain_lease_potential, days_on_market, price_per_sqft,
              created_at, updated_at FROM properties WHERE id = $1`

    var property database.Property
    err := database.DB.QueryRow(query, propertyID).Scan(
        &property.ID, &property.Address, &property.Price, &property.Size, &property.Zoning,
        &property.Latitude, &property.Longitude, &property.Description, &property.Habitability,
        &property.Proximity, &property.ChainLeasePotential, &property.DaysOnMarket,
        &property.PricePerSqFt, &property.CreatedAt, &property.UpdatedAt,
    )

    if err != nil {
        return nil, err
    }

    return &property, nil
}

// PropertyDetailsHandler handles detailed property information requests
func PropertyDetailsHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodGet {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    // Get address from query parameter
    address := r.URL.Query().Get("address")
    if address == "" {
        http.Error(w, "Address parameter is required", http.StatusBadRequest)
        return
    }

    // Use the details package to get comprehensive property information
    propertyDetails, err := details.GetPropertyDetails(address)
    if err != nil {
        if strings.Contains(err.Error(), "not found") {
            http.Error(w, "Property not found", http.StatusNotFound)
            return
        }
        http.Error(w, "Failed to retrieve property details: "+err.Error(), http.StatusInternalServerError)
        return
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "success": true,
        "data":    propertyDetails,
    })
}

// HealthCheckHandler provides a health check endpoint for API monitoring
func HealthCheckHandler(w http.ResponseWriter, r *http.Request) {
    if r.Method != http.MethodGet {
        http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
        return
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(map[string]interface{}{
        "success": true,
        "status":  "healthy",
        "message": "PropBolt API is running",
    })
}
