declare const CsvGeneratorApi: {
    readonly body: {
        readonly type: "object";
        readonly properties: {
            readonly file_name: {
                readonly type: "string";
                readonly description: "5-60 characters";
            };
            readonly map: {
                readonly type: "array";
                readonly items: {
                    readonly type: "string";
                };
            };
            readonly webcomplete_url: {
                readonly type: "string";
            };
            readonly count: {
                readonly type: "boolean";
            };
            readonly ids: {
                readonly type: "array";
                readonly items: {
                    readonly type: "integer";
                    readonly format: "int32";
                    readonly minimum: -**********;
                    readonly maximum: **********;
                };
            };
            readonly size: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly resultIndex: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly address: {
                readonly type: "string";
            };
            readonly house: {
                readonly type: "string";
            };
            readonly street: {
                readonly type: "string";
            };
            readonly city: {
                readonly type: "string";
            };
            readonly county: {
                readonly type: "string";
            };
            readonly state: {
                readonly type: "string";
            };
            readonly zip: {
                readonly type: "string";
            };
            readonly latitude: {
                readonly type: "number";
                readonly format: "float";
                readonly minimum: -3.402823669209385e+38;
                readonly maximum: 3.402823669209385e+38;
            };
            readonly longitude: {
                readonly type: "number";
                readonly format: "float";
                readonly minimum: -3.402823669209385e+38;
                readonly maximum: 3.402823669209385e+38;
            };
            readonly radius: {
                readonly type: "number";
                readonly format: "float";
                readonly minimum: -3.402823669209385e+38;
                readonly maximum: 3.402823669209385e+38;
            };
            readonly polygon: {
                readonly type: "array";
                readonly items: {
                    readonly properties: {
                        readonly lat: {
                            readonly type: "number";
                            readonly format: "double";
                            readonly minimum: -1.7976931348623157e+308;
                            readonly maximum: 1.7976931348623157e+308;
                        };
                        readonly lon: {
                            readonly type: "number";
                            readonly format: "double";
                            readonly minimum: -1.7976931348623157e+308;
                            readonly maximum: 1.7976931348623157e+308;
                        };
                    };
                    readonly type: "object";
                };
            };
            readonly multi_polygon: {
                readonly type: "array";
                readonly items: {
                    readonly properties: {
                        readonly boundaries: {
                            readonly type: "array";
                            readonly items: {
                                readonly properties: {
                                    readonly lat: {
                                        readonly type: "number";
                                        readonly format: "double";
                                        readonly minimum: -1.7976931348623157e+308;
                                        readonly maximum: 1.7976931348623157e+308;
                                    };
                                    readonly lon: {
                                        readonly type: "number";
                                        readonly format: "double";
                                        readonly minimum: -1.7976931348623157e+308;
                                        readonly maximum: 1.7976931348623157e+308;
                                    };
                                };
                                readonly type: "object";
                            };
                        };
                    };
                    readonly type: "object";
                };
            };
            readonly property_type: {
                readonly type: "string";
                readonly enum: readonly ["SFR", "MFR", "LAND", "CONDO", "MOBILE", "OTHER"];
            };
            readonly property_use_code: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly mls_active: {
                readonly type: "boolean";
            };
            readonly mls_pending: {
                readonly type: "boolean";
            };
            readonly mls_cancelled: {
                readonly type: "boolean";
            };
            readonly mls_days_on_market_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly mls_days_on_market_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly mls_listing_price_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly mls_listing_price_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly absentee_owner: {
                readonly type: "boolean";
            };
            readonly adjustable_rate: {
                readonly type: "boolean";
            };
            readonly assumable: {
                readonly type: "boolean";
            };
            readonly attic: {
                readonly type: "boolean";
            };
            readonly auction: {
                readonly type: "boolean";
            };
            readonly basement: {
                readonly type: "boolean";
            };
            readonly breezeway: {
                readonly type: "boolean";
            };
            readonly carport: {
                readonly type: "boolean";
            };
            readonly cash_buyer: {
                readonly type: "boolean";
            };
            readonly corporate_owned: {
                readonly type: "boolean";
            };
            readonly death: {
                readonly type: "boolean";
            };
            readonly deck: {
                readonly type: "boolean";
            };
            readonly feature_balcony: {
                readonly type: "boolean";
            };
            readonly fire_sprinklers: {
                readonly type: "boolean";
            };
            readonly flood_zone: {
                readonly type: "boolean";
            };
            readonly foreclosure: {
                readonly type: "boolean";
            };
            readonly free_clear: {
                readonly type: "boolean";
            };
            readonly garage: {
                readonly type: "boolean";
            };
            readonly high_equity: {
                readonly type: "boolean";
            };
            readonly inherited: {
                readonly type: "boolean";
            };
            readonly in_state_owner: {
                readonly type: "boolean";
            };
            readonly investor_buyer: {
                readonly type: "boolean";
            };
            readonly judgment: {
                readonly type: "boolean";
            };
            readonly mfh_2to4: {
                readonly type: "boolean";
            };
            readonly mfh_5plus: {
                readonly type: "boolean";
            };
            readonly negative_equity: {
                readonly type: "boolean";
            };
            readonly out_of_state_owner: {
                readonly type: "boolean";
            };
            readonly patio: {
                readonly type: "boolean";
            };
            readonly pool: {
                readonly type: "boolean";
            };
            readonly pre_foreclosure: {
                readonly type: "boolean";
            };
            readonly prior_owner_individual: {
                readonly type: "boolean";
            };
            readonly private_lender: {
                readonly type: "boolean";
            };
            readonly quit_claim: {
                readonly type: "boolean";
            };
            readonly reo: {
                readonly type: "boolean";
            };
            readonly rv_parking: {
                readonly type: "boolean";
            };
            readonly tax_lien: {
                readonly type: "boolean";
            };
            readonly trust_owned: {
                readonly type: "boolean";
            };
            readonly vacant: {
                readonly type: "boolean";
            };
            readonly census_block: {
                readonly type: "string";
            };
            readonly census_block_group: {
                readonly type: "string";
            };
            readonly census_tract: {
                readonly type: "string";
            };
            readonly construction: {
                readonly type: "string";
            };
            readonly document_type_code: {
                readonly type: "string";
            };
            readonly flood_zone_type: {
                readonly type: "string";
            };
            readonly loan_type_code_first: {
                readonly type: "string";
            };
            readonly loan_type_code_second: {
                readonly type: "string";
            };
            readonly loan_type_code_third: {
                readonly type: "string";
            };
            readonly notice_type: {
                readonly type: "string";
                readonly enum: readonly ["FOR", "NOD", "NOL", "NTS", "REO"];
            };
            readonly parcel_account_number: {
                readonly type: "string";
            };
            readonly search_range: {
                readonly type: "string";
            };
            readonly sewage: {
                readonly type: "string";
            };
            readonly water_source: {
                readonly type: "string";
            };
            readonly estimated_equity: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly equity_operator: {
                readonly type: "string";
                readonly enum: readonly ["gt", "gte", "lt", "lte"];
            };
            readonly equity_percent: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly equity_percent_operator: {
                readonly type: "string";
                readonly enum: readonly ["gt", "gte", "lt", "lte"];
            };
            readonly last_sale_date: {
                readonly type: "string";
                readonly format: "date";
            };
            readonly last_sale_date_operator: {
                readonly type: "string";
                readonly enum: readonly ["gt", "gte", "lt", "lte"];
            };
            readonly median_income: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly median_income_operator: {
                readonly type: "string";
                readonly enum: readonly ["gt", "gte", "lt", "lte"];
            };
            readonly years_owned: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly years_owned_operator: {
                readonly type: "string";
                readonly enum: readonly ["gt", "gte", "lt", "lte"];
            };
            readonly assessed_improvement_value_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly assessed_improvement_value_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly assessed_land_value_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly assessed_land_value_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly assessed_value_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly assessed_value_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly auction_date_min: {
                readonly type: "string";
                readonly format: "date";
            };
            readonly auction_date_max: {
                readonly type: "string";
                readonly format: "date";
            };
            readonly baths_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly baths_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly beds_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly beds_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly building_size_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly building_size_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly deck_area_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly deck_area_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly estimated_equity_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly estimated_equity_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly foreclosure_date_min: {
                readonly type: "string";
                readonly format: "date";
            };
            readonly foreclosure_date_max: {
                readonly type: "string";
                readonly format: "date";
            };
            readonly last_sale_date_min: {
                readonly type: "string";
                readonly format: "date";
            };
            readonly last_sale_date_max: {
                readonly type: "string";
                readonly format: "date";
            };
            readonly last_sale_price_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly last_sale_price_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly lot_size_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly lot_size_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly ltv_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly ltv_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly median_income_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly median_income_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly mortgage_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly mortgage_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly rooms_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly rooms_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly pool_area_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly pool_area_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly portfolio_equity_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly portfolio_equity_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly portfolio_mortgage_balance_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly portfolio_mortgage_balance_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly portfolio_purchased_last12_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly portfolio_purchased_last12_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly portfolio_value_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly portfolio_value_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly pre_foreclosure_date_min: {
                readonly type: "string";
                readonly format: "date";
            };
            readonly pre_foreclosure_date_max: {
                readonly type: "string";
                readonly format: "date";
            };
            readonly prior_owner_months_owned_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly prior_owner_months_owned_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly properties_owned_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly properties_owned_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly stories_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly stories_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly tax_delinquent_year_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly tax_delinquent_year_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly units_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly units_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly value_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly value_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly year_built_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly year_built_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly years_owned_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly years_owned_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
        };
        readonly $schema: "https://json-schema.org/draft/2020-12/schema#";
    };
    readonly response: {
        readonly "200": {
            readonly type: "object";
            readonly properties: {};
            readonly $schema: "https://json-schema.org/draft/2020-12/schema#";
        };
        readonly "400": {
            readonly type: "object";
            readonly properties: {};
            readonly $schema: "https://json-schema.org/draft/2020-12/schema#";
        };
    };
};
declare const InvoluntaryLienApi: {
    readonly body: {
        readonly type: "object";
        readonly properties: {
            readonly id: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly address: {
                readonly type: "string";
            };
            readonly zip: {
                readonly type: "string";
            };
            readonly apn: {
                readonly type: "string";
            };
            readonly fips: {
                readonly type: "string";
            };
        };
        readonly $schema: "https://json-schema.org/draft/2020-12/schema#";
    };
    readonly response: {
        readonly "200": {
            readonly type: "object";
            readonly properties: {};
            readonly $schema: "https://json-schema.org/draft/2020-12/schema#";
        };
        readonly "400": {
            readonly type: "object";
            readonly properties: {};
            readonly $schema: "https://json-schema.org/draft/2020-12/schema#";
        };
    };
};
declare const PropertyDetailApi1: {
    readonly body: {
        readonly type: "object";
        readonly properties: {
            readonly id: {
                readonly type: "string";
                readonly description: "The \"id\" property from the Property Search API result objects. If using this unique property ID for Property Detail, then no other fields are required.";
            };
            readonly comps: {
                readonly type: "boolean";
                readonly description: "Set to true if you would like to have houses nearby with conferable valuations returned as part of the response. ***Includes RealEstateAPI's special AVM for the property***";
                readonly default: false;
            };
            readonly exact_match: {
                readonly type: "boolean";
                readonly description: "only return matches that match your exact address or address parts, no fuzzy matching";
            };
            readonly address: {
                readonly type: "string";
                readonly description: "A fully formatted address with the following format: 123 Main St, Arlington VA 22205. When this field is provided and is in the valid format, no other fields are required";
            };
            readonly house: {
                readonly type: "string";
                readonly description: "House number field is to be used with the street, city, state, and zip fields (& unit if applicable)";
            };
            readonly unit: {
                readonly type: "string";
                readonly description: "House unit number (for Apt. #'s, Suite #'s, etc.)";
            };
            readonly street: {
                readonly type: "string";
                readonly description: "The street name of the property (e.g. Main St)";
            };
            readonly city: {
                readonly type: "string";
                readonly description: "The city where the property is located";
            };
            readonly state: {
                readonly type: "string";
                readonly description: "The 2 character state code for the property's state (e.g. VA = Virginia)";
            };
            readonly county: {
                readonly type: "string";
                readonly description: "(*This field is not required for a fully formatted address*) The county where your search property is located.";
            };
            readonly zip: {
                readonly type: "string";
                readonly description: "Zipcodes need to be 5 digits";
            };
            readonly apn: {
                readonly type: "string";
                readonly description: "The assessor's parcel number (APN) is a unique identifier for an address/parcel & is particularly helpful when looking up Land Parcels without House Numbers";
            };
            readonly fips: {
                readonly type: "string";
                readonly description: "String of Length 5";
            };
        };
        readonly $schema: "https://json-schema.org/draft/2020-12/schema#";
    };
    readonly metadata: {
        readonly allOf: readonly [{
            readonly type: "object";
            readonly properties: {
                readonly "x-user-id": {
                    readonly type: "string";
                    readonly $schema: "https://json-schema.org/draft/2020-12/schema#";
                    readonly description: "Denote a unique user identifier to this api call by passing it in this header field";
                };
            };
            readonly required: readonly [];
        }];
    };
    readonly response: {
        readonly "200": {
            readonly $schema: "https://json-schema.org/draft/2020-12/schema#";
        };
        readonly "400": {
            readonly $schema: "https://json-schema.org/draft/2020-12/schema#";
        };
        readonly "401": {
            readonly $schema: "https://json-schema.org/draft/2020-12/schema#";
        };
        readonly "404": {
            readonly $schema: "https://json-schema.org/draft/2020-12/schema#";
        };
        readonly "429": {
            readonly $schema: "https://json-schema.org/draft/2020-12/schema#";
        };
        readonly "500": {
            readonly $schema: "https://json-schema.org/draft/2020-12/schema#";
        };
    };
};
declare const PropertyDetailBulkApi: {
    readonly body: {
        readonly type: "object";
        readonly required: readonly ["ids"];
        readonly properties: {
            readonly ids: {
                readonly type: "array";
                readonly description: "List of property ids returned in the response of our Property Search API";
                readonly items: {
                    readonly type: "integer";
                    readonly format: "int32";
                    readonly minimum: -**********;
                    readonly maximum: **********;
                };
            };
        };
        readonly $schema: "https://json-schema.org/draft/2020-12/schema#";
    };
    readonly metadata: {
        readonly allOf: readonly [{
            readonly type: "object";
            readonly properties: {
                readonly "x-user-id": {
                    readonly type: "string";
                    readonly $schema: "https://json-schema.org/draft/2020-12/schema#";
                    readonly description: "Denote a unique user identifier to this api call by passing it in this header field";
                };
            };
            readonly required: readonly [];
        }];
    };
    readonly response: {
        readonly "200": {
            readonly $schema: "https://json-schema.org/draft/2020-12/schema#";
        };
    };
};
declare const PropertySearchApi: {
    readonly body: {
        readonly type: "object";
        readonly properties: {
            readonly count: {
                readonly type: "boolean";
                readonly description: "Set to true to only return the count for the total  number of records that would be returned for the search and not the records themselves.";
            };
            readonly ids: {
                readonly type: "array";
                readonly description: "Provide a list of property IDs from past or saved Property Searches to pull back all of the enriched fields";
                readonly items: {
                    readonly type: "integer";
                    readonly format: "int32";
                    readonly minimum: -**********;
                    readonly maximum: **********;
                };
            };
            readonly ids_only: {
                readonly type: "boolean";
                readonly description: "Returns up to 10,000 property IDs matching your search criteria. When provided, the \"size\" and \"resultIndex\" will be ignored.";
                readonly default: false;
            };
            readonly obfuscate: {
                readonly type: "boolean";
                readonly description: "Will remove the address and name fields on the properties returned";
                readonly default: false;
            };
            readonly sort: {
                readonly type: "object";
                readonly description: "Sorts result set based on user defined sorting definitions across the Property Search fields";
                readonly properties: {};
            };
            readonly summary: {
                readonly type: "boolean";
                readonly description: "Returns an aggregation of all lead types in a summary object. The summary object will return totals for each lead type within the context of the given search.";
                readonly default: false;
            };
            readonly resultIndex: {
                readonly type: "integer";
                readonly description: "Used with size to accomplish paging.  The server will skip the number of records specified by resultIndex, and return the records starting after the resultIndex.  The total number of records returned will not be greater than the size specified, or a max of 250 set by the server.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly size: {
                readonly type: "integer";
                readonly description: "Set to the maximum number of records that the server can return for the search.  Used in conjunction with resultIndex for paging results.";
                readonly default: 50;
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly address: {
                readonly type: "string";
                readonly description: "Fully formatted address for a property search.  This should include house, street, city, state and zip";
            };
            readonly house: {
                readonly type: "string";
                readonly description: "Used to search for specific house numbers.   Must be accompanied with state or zip to limit results.";
            };
            readonly street: {
                readonly type: "string";
                readonly description: "Used to search searching street names only.  Must be accompanied with state or zip to limit results.";
            };
            readonly city: {
                readonly type: "string";
                readonly description: "Used to search within a city only.  Must be accompanied with state or zip to limit results.";
            };
            readonly state: {
                readonly type: "string";
                readonly description: "Used to search within a state.  Must be accompanied by city, house, or street to limit results.";
            };
            readonly county: {
                readonly type: "string";
                readonly description: "Used to search within a county.  Must be accompanied by state, or zip.";
            };
            readonly zip: {
                readonly type: "string";
                readonly description: "Used to search within a US zip code. An array of zips (of type:string) can also be provided to this field.";
            };
            readonly latitude: {
                readonly type: "number";
                readonly description: "If latitude & longitude are provided, the search radius will be calculated with that set of coordinates as center";
                readonly format: "float";
                readonly minimum: -3.402823669209385e+38;
                readonly maximum: 3.402823669209385e+38;
            };
            readonly longitude: {
                readonly type: "number";
                readonly description: "If latitude & longitude are provided, the search radius will be calculated with that set of coordinates as center";
                readonly format: "float";
                readonly minimum: -3.402823669209385e+38;
                readonly maximum: 3.402823669209385e+38;
            };
            readonly radius: {
                readonly type: "number";
                readonly description: "Provide a search radius between 0.1-10 miles for narrowing your search";
                readonly format: "float";
                readonly minimum: -3.402823669209385e+38;
                readonly maximum: 3.402823669209385e+38;
            };
            readonly polygon: {
                readonly type: "array";
                readonly description: "Provide an array of latitude/longitude pairs for the Geo portion of your query";
                readonly items: {
                    readonly properties: {
                        readonly lat: {
                            readonly type: "number";
                            readonly format: "double";
                            readonly minimum: -1.7976931348623157e+308;
                            readonly maximum: 1.7976931348623157e+308;
                        };
                        readonly lon: {
                            readonly type: "number";
                            readonly format: "double";
                            readonly minimum: -1.7976931348623157e+308;
                            readonly maximum: 1.7976931348623157e+308;
                        };
                    };
                    readonly type: "object";
                };
            };
            readonly multi_polygon: {
                readonly type: "array";
                readonly description: "Minimum of 1 polygon";
                readonly items: {
                    readonly properties: {
                        readonly boundaries: {
                            readonly type: "array";
                            readonly items: {
                                readonly properties: {
                                    readonly lat: {
                                        readonly type: "number";
                                        readonly format: "double";
                                        readonly minimum: -1.7976931348623157e+308;
                                        readonly maximum: 1.7976931348623157e+308;
                                    };
                                    readonly lon: {
                                        readonly type: "number";
                                        readonly format: "double";
                                        readonly minimum: -1.7976931348623157e+308;
                                        readonly maximum: 1.7976931348623157e+308;
                                    };
                                };
                                readonly type: "object";
                            };
                        };
                    };
                    readonly type: "object";
                };
            };
            readonly property_type: {
                readonly type: "string";
                readonly description: "Provide the type of residences/properties you are looking for";
                readonly enum: readonly ["SFR", "MFR", "LAND", "CONDO", "MOBILE", "OTHER"];
            };
            readonly property_use_code: {
                readonly type: "integer";
                readonly description: "Also accepts an Array of Integers, where each integer is one of our accepted Property Use Codes. See all codes here: https://developer.realestateapi.com/reference/property-use-codes-reference";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly mls_active: {
                readonly type: "boolean";
                readonly description: "Find active MLS listings";
            };
            readonly mls_pending: {
                readonly type: "boolean";
                readonly description: "Find pending MLS sales that are expected to close";
            };
            readonly mls_cancelled: {
                readonly type: "boolean";
                readonly description: "Find terminated MLS listings";
            };
            readonly mls_sold: {
                readonly type: "boolean";
                readonly description: "Find sold MLS listings";
            };
            readonly mls_days_on_market_min: {
                readonly type: "integer";
                readonly description: "Find properties that have been on the market for a certain amount of days. Use with \"mls_active\": true, \"mls_pending\": true or \"mls_cancelled\": true";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly mls_days_on_market_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly mls_listing_price_min: {
                readonly type: "integer";
                readonly description: "Lower bound used with mls_listing_max to only find properties with MLS listing prices within a defined range";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly mls_listing_price_max: {
                readonly type: "integer";
                readonly description: "Minimum value of 1";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly mls_listing_price: {
                readonly type: "integer";
                readonly description: "The official MLS listing price for the property";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly mls_listing_price_operator: {
                readonly type: "string";
                readonly description: "mls_operator is to be used with mls_listing_price to indicate a range less than or greater than starting with that listing price. For example, { mls_listing_price: 100000, mls_operator: 'gte' } would retrieve all properties with an MLS listing price of $100,000 or more";
                readonly enum: readonly ["lt", "lte", "gt", "gte"];
            };
            readonly id: {
                readonly type: "string";
                readonly description: "AutoComplete Field. Can be a string or an integer. Represents the unique property id in the case of full address autocomplete searches.";
            };
            readonly apn: {
                readonly type: "string";
                readonly description: "AutoComplete Field. The Property's unique tax assessor identifier, returned as part of the AutoComplete API response.";
            };
            readonly stateId: {
                readonly type: "string";
                readonly description: "AutoComplete Field.";
            };
            readonly countyId: {
                readonly type: "string";
                readonly description: "AutoComplete Field.";
            };
            readonly neighborhood_id: {
                readonly type: "integer";
                readonly description: "Autocomplete field.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly neighborhood_name: {
                readonly type: "string";
                readonly description: "Autocomplete field.";
            };
            readonly searchType: {
                readonly type: "string";
                readonly description: "AutoComplete Field. A = full address ; C = city ; N = county; S = street ; Z = zip; G = neighborhood; T = state";
                readonly enum: readonly ["A", "C", "N", "S", "Z", "G", "T"];
            };
            readonly fips: {
                readonly type: "string";
                readonly description: "AutoComplete Field.";
            };
            readonly title: {
                readonly type: "string";
                readonly description: "AutoComplete Field.";
            };
            readonly usps_mail_state: {
                readonly type: "string";
                readonly description: "Filter for properties with a specific USPS mailing state";
            };
            readonly absentee_owner: {
                readonly type: "boolean";
                readonly description: "Used for searching for properties where the owner is not currently a resident.  Generally signifies a tenant or non-owner occupied property.";
            };
            readonly adjustable_rate: {
                readonly type: "boolean";
                readonly description: "Indicates if the current mortgage on the property has an adjustable rate.";
            };
            readonly assumable: {
                readonly type: "boolean";
                readonly description: "Indicates if the mortgage on a given property is assumable.";
            };
            readonly attic: {
                readonly type: "boolean";
            };
            readonly auction: {
                readonly type: "boolean";
                readonly description: "Used to find properties with an auction date.  Used with search_range or a default max of 1 year.";
            };
            readonly basement: {
                readonly type: "boolean";
                readonly description: "Used to find properties with a basement.";
            };
            readonly breezeway: {
                readonly type: "boolean";
            };
            readonly carport: {
                readonly type: "boolean";
                readonly description: "Indicates properties with a carport structure.";
            };
            readonly cash_buyer: {
                readonly type: "boolean";
                readonly description: "Indicates if the property ownership is subsequent to an all cash transaction";
            };
            readonly corporate_owned: {
                readonly type: "boolean";
                readonly description: "Used to find properties where one of the owners is company.";
            };
            readonly death: {
                readonly type: "boolean";
                readonly description: "Used to find properties where the property owner on the deed is recently deceased. Can be used for probate lists.";
            };
            readonly deck: {
                readonly type: "boolean";
                readonly description: "Used to find properties that have a deck";
            };
            readonly feature_balcony: {
                readonly type: "boolean";
                readonly description: "Used to find properties with a balcony.";
            };
            readonly fire_sprinklers: {
                readonly type: "boolean";
                readonly description: "Used to find properties with registered fire sprinkler fixtures.";
            };
            readonly flood_zone: {
                readonly type: "boolean";
                readonly description: "Indicates if the property is in a flood zone area. This flag can be used in conjunction with \"flood_zone_type\" to get more specific result sets.";
            };
            readonly foreclosure: {
                readonly type: "boolean";
                readonly description: "Used to find properties in foreclosure.  Used with search_range or a default max of 1 year.";
            };
            readonly free_clear: {
                readonly type: "boolean";
                readonly description: "Used to find properties without an open mortgage.";
            };
            readonly garage: {
                readonly type: "boolean";
                readonly description: "Used to find properties with a physical structure marked for garage use.";
            };
            readonly high_equity: {
                readonly type: "boolean";
                readonly description: "Indicates properties with high equity (>39%)";
            };
            readonly hoa: {
                readonly type: "boolean";
                readonly description: "Filter for properties that have an HOA";
            };
            readonly individual_owned: {
                readonly type: "boolean";
                readonly description: "Filter for properties only owned by an Individual (not LLC or Trust)";
            };
            readonly inherited: {
                readonly type: "boolean";
                readonly description: "Set to true to search inherited properties";
            };
            readonly in_state_owner: {
                readonly type: "boolean";
                readonly description: "Used to find properties with an owner whose mailing address is in the same state as the property address.";
            };
            readonly investor_buyer: {
                readonly type: "boolean";
                readonly description: "Signals that the property was cash purchased by an absentee owner/investor, rather than individual like with the cash_buyer flag";
            };
            readonly judgment: {
                readonly type: "boolean";
                readonly description: "Used to find properties where a lawsuit has been filed against a property owner or a party involved in a real estate transaction, and the court rules in favor of one of the parties, and issued a judgment.";
            };
            readonly last_sale_arms_length: {
                readonly type: "boolean";
                readonly description: "Setting this to \"true\" will filter out transfers and other non-arms length transaction types. Use in conjunction with last_sale_date_min/max";
            };
            readonly mfh_2to4: {
                readonly type: "boolean";
                readonly description: "Multi-family homes with 2 to 4 units";
            };
            readonly mfh_5plus: {
                readonly type: "boolean";
                readonly description: "Multi-family homes with 5 or more units";
            };
            readonly out_of_state_owner: {
                readonly type: "boolean";
                readonly description: "Used to find properties with an owner whose mailing address is in a different state as the property address.";
            };
            readonly patio: {
                readonly type: "boolean";
                readonly description: "Used to find properties with a patio";
            };
            readonly pool: {
                readonly type: "boolean";
                readonly description: "Used to find properties with a pool";
            };
            readonly pre_foreclosure: {
                readonly type: "boolean";
                readonly description: "Used to find poperties that have received any notice of preforeclosure.   Used with search_range or a default max of 1 year.";
            };
            readonly prior_owner_individual: {
                readonly type: "boolean";
                readonly description: "Helps determine what properties are the result of a Flip. Use with \"prior_owner_months_owned_min\"/\"prior_owner_months_owned_max\"";
            };
            readonly private_lender: {
                readonly type: "boolean";
                readonly description: "Returns all properties that are currently financed by a private lender";
            };
            readonly quit_claim: {
                readonly type: "boolean";
                readonly description: "Indicates if the property ownership was subsequent to a quit claim";
            };
            readonly reo: {
                readonly type: "boolean";
                readonly description: "Used to find properties owned by a bank, trust, services entity, or tax entity.  Used with search_range or a default max of 1 year.";
            };
            readonly rv_parking: {
                readonly type: "boolean";
                readonly description: "The property is designated as having RV Parking";
            };
            readonly tax_lien: {
                readonly type: "boolean";
                readonly description: "Find properties where there is a tax lien against the property";
            };
            readonly trust_owned: {
                readonly type: "boolean";
                readonly description: "The property is owned by a Trust";
            };
            readonly vacant: {
                readonly type: "boolean";
                readonly description: "Used to find properties that are vacant";
            };
            readonly census_block: {
                readonly type: "string";
                readonly description: "Values 1000-5000";
            };
            readonly census_block_group: {
                readonly type: "string";
                readonly description: "Values 0-10";
            };
            readonly census_tract: {
                readonly type: "string";
                readonly description: "Official tract number from the U.S. Census Bureau";
            };
            readonly construction: {
                readonly type: "string";
                readonly description: "Full list of construction types: https://developer.realestateapi.com/reference/construction-types";
            };
            readonly document_type_code: {
                readonly type: "string";
                readonly description: "Used to find a specific document type for more granular searches other than the booleans provided.  This field can also be assigned an array of document type codes. Used in conjunction with search_range, or a maximum default value of 1 year.";
            };
            readonly flood_zone_type: {
                readonly type: "string";
                readonly description: "B, C, X (for moderate to low risk areas); A, AE, A1-30, AH, AO, AR, A99, V, VE, V1 - V30 (High Risk - Coastal Areas); D (Undetermined Risk Zone)";
            };
            readonly loan_type_code_first: {
                readonly type: "string";
                readonly description: "Refer to the Loan Codes that are searchable://developer.realestateapi.com/reference/loan-type-codes";
            };
            readonly loan_type_code_second: {
                readonly type: "string";
            };
            readonly loan_type_code_third: {
                readonly type: "string";
            };
            readonly notice_type: {
                readonly type: "string";
                readonly description: "Search by the Recording Date of the .foreclosureInfo data for the specified notice type";
                readonly enum: readonly ["FOR", "NOD", "NOL", "NTS", "REO"];
            };
            readonly parcel_account_number: {
                readonly type: "string";
                readonly description: "e.g. 05-00925.01";
            };
            readonly roof_construction_code: {
                readonly type: "integer";
                readonly description: "Accepts an array or single integer code value. See all possible values here: https://developer.realestateapi.com/reference/roof-construction-codes";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly roof_material_code: {
                readonly type: "integer";
                readonly description: "Accepts an array or single integer code value. See all possible values here: https://developer.realestateapi.com/reference/roof-materials-codes";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly search_range: {
                readonly type: "string";
                readonly description: "Used in conjunction for reo, auction, foreclosure, and preforeclosure searches to limit the search to only return records where the event happened within the provided range.  All ranges work from NOW back to the provided range.";
                readonly enum: readonly ["1_MONTH", "2_MONTH", "3_MONTH", "6_MONTH", "1_YEAR"];
            };
            readonly sewage: {
                readonly type: "string";
                readonly description: "Options: Municipal, Yes, Septic, None, Storm";
            };
            readonly water_source: {
                readonly type: "string";
                readonly description: "Full list of water source types you can filter by: https://developer.realestateapi.com/reference/water-source-searches";
            };
            readonly estimated_equity: {
                readonly type: "integer";
                readonly description: "Used in conjunction with the equity_percent_operator to find properties where the estimated equity amount is greater than or less than the value provided.  Equity dollar amount is computed as the difference of the estimated value less any known open mortgages.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly equity_operator: {
                readonly type: "string";
                readonly description: "Comparison operator for searches using estimated_equity.  Returns properties based on a greater than, or less than operation coupled with the value provided for estimated_equity which is based on total dollars of equity estimated from the estimated value and any known open mortgages.";
                readonly enum: readonly ["lt", "lte", "gt", "gte"];
            };
            readonly equity_percent: {
                readonly type: "integer";
                readonly description: "Used in conjunction with the equity_percent_operator to find properties where the equity percentage is greater than or less than the value provided.  Equity percentage is a based on the difference of the computed LTV.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly equity_percent_operator: {
                readonly type: "string";
                readonly description: "Comparison operator for searches using equity_percent.  Returns properties based on a greater than, or less than operation coupled with the value provided for equity_percent which is based on the difference of the calculated LTV.";
                readonly enum: readonly ["lt", "lte", "gt", "gte"];
            };
            readonly last_sale_date: {
                readonly type: "string";
                readonly description: "Find properties based on the date of the last sale history transaction";
                readonly format: "date";
            };
            readonly last_sale_date_operator: {
                readonly type: "string";
                readonly description: "Used in conjunction with \"last_sale_date\" to find properties that satisfy the range for when they were last sold in a transaction.";
                readonly enum: readonly ["lt", "lte", "gt", "gte"];
            };
            readonly median_income: {
                readonly type: "integer";
                readonly description: "Find properties based on the median income of the Areas that contain the properties";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly median_income_operator: {
                readonly type: "string";
                readonly description: "Used in conjunction with the \"median_income\" field in order to specify the range lower or higher you want to look at from the given median_income.";
                readonly enum: readonly ["lt", "lte", "gt", "gte"];
            };
            readonly years_owned: {
                readonly type: "integer";
                readonly description: "Number value of the years owned you are searching for. To be used with years_owned_operator";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly years_owned_operator: {
                readonly type: "string";
                readonly description: "Operator for less than and greater than searches on years_owned field";
                readonly enum: readonly ["lt", "lte", "gt", "gte"];
            };
            readonly assessed_improvement_value_min: {
                readonly type: "integer";
                readonly description: "Value range search against the county assessed improvement value";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly assessed_improvement_value_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly assessed_land_value_min: {
                readonly type: "integer";
                readonly description: "Value range search against the county assessed land value";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly assessed_land_value_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly assessed_value_min: {
                readonly type: "integer";
                readonly description: "Value range search against the county assessed value";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly assessed_value_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly auction_date_min: {
                readonly type: "string";
                readonly description: "filter on dates of upcoming foreclosure auctions (e.g. Current Date \"2024-05-01\" & set a future date range of \"2024-05-15\" to \"2024-05-30\"). Use with \"auction\": true";
                readonly format: "date";
            };
            readonly auction_date_max: {
                readonly type: "string";
                readonly format: "date";
            };
            readonly baths_min: {
                readonly type: "integer";
                readonly description: "Used for searching a range of properties with bathrooms between a min and max.  Minimum numbers of bathrooms for the given property search";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly baths_max: {
                readonly type: "integer";
                readonly description: "Used for searching a range of properties with bathrooms between a min and max.  Maximum numbers of bathrooms for the given property search";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly beds_min: {
                readonly type: "integer";
                readonly description: "Used for searching a range of properties with bedrooms between a min and max.  Minimum numbers of bedrooms for the given property search";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly beds_max: {
                readonly type: "integer";
                readonly description: "Used for searching a range of properties with bedrooms between a min and max.  Maximum numbers of bedrooms for the given property search";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly building_size_min: {
                readonly type: "integer";
                readonly description: "Used for searching a range of properties with an interior, living square footage between a min and max.  Minimum square footage of the interior living space for the given property search";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly building_size_max: {
                readonly type: "integer";
                readonly description: "Used for searching a range of properties with an interior, living square footage between a min and max.  Maximum square footage of the interior living space for the given property search";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly deck_area_min: {
                readonly type: "integer";
                readonly description: "In sq. ft.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly deck_area_max: {
                readonly type: "string";
                readonly description: "In sq. ft.";
            };
            readonly estimated_equity_min: {
                readonly type: "integer";
                readonly description: "Filter for properties based on the nominal value of equity owners have in their homes. Works well with \"value_min\"/\"value_max\".";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly estimated_equity_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly foreclosure_date_min: {
                readonly type: "string";
                readonly description: "Filter for properties based on a date range for when a specific Foreclosure document was recorded - use with \"foreclosure\": true & \"notice_type\"";
                readonly format: "date";
            };
            readonly foreclosure_date_max: {
                readonly type: "string";
                readonly format: "date";
            };
            readonly last_sale_date_min: {
                readonly type: "string";
                readonly description: "Minimum Date for the last sale transaction date";
                readonly format: "date";
            };
            readonly last_sale_date_max: {
                readonly type: "string";
                readonly description: "Maximum Date for the last sale transaction date";
                readonly format: "date";
            };
            readonly last_sale_price_min: {
                readonly type: "integer";
                readonly description: "Filter for properties based on a Last Sale Price range.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly last_sale_price_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly lot_size_min: {
                readonly type: "integer";
                readonly description: "Used for searching a range of properties with lot sizes between a min and max.  Minimum square footage of the exterior lot  built for the given property search";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly lot_size_max: {
                readonly type: "integer";
                readonly description: "Used for searching a range of properties with lot sizes between a min and max.  Maximum square footage of the exterior lot  built for the given property search";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly ltv_min: {
                readonly type: "integer";
                readonly description: "Min. of 0";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly ltv_max: {
                readonly type: "string";
                readonly description: "Max of 100";
            };
            readonly median_income_min: {
                readonly type: "integer";
                readonly description: "Filter for properties that are within a certain range of median income (Zipcode-level)";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly median_income_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly mortgage_min: {
                readonly type: "integer";
                readonly description: "Used for searching a range of properties with an estimated total of open mortgages between a min and max.  Minimum estimated amount for all open mortgages for the given property search.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly mortgage_max: {
                readonly type: "integer";
                readonly description: "Used for searching a range of properties with an estimated total of open mortgages between a min and max.  Maximum estimated amount for all open mortgages for the given property search.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly rooms_min: {
                readonly type: "integer";
                readonly description: "Used for setting the minimum on the number of total rooms you want your properties to have.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly rooms_max: {
                readonly type: "integer";
                readonly description: "Used for setting the maximum on the number of total rooms you want your properties to have.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly pool_area_min: {
                readonly type: "integer";
                readonly description: "In sq. ft.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly pool_area_max: {
                readonly type: "integer";
                readonly description: "In sq. ft.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly portfolio_equity_min: {
                readonly type: "integer";
                readonly description: "Used to find properties where the minimum ownership interest or the stake that an investor has in the portfolio is as specified. Portfolio equity is the difference between the total value of the portfolio and any outstanding debts or liabilities related to the portfolio.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly portfolio_equity_max: {
                readonly type: "integer";
                readonly description: "Used to find properties where the maximum ownership interest or the stake that an investor has in the portfolio is as specified. Portfolio equity is the difference between the total value of the portfolio and any outstanding debts or liabilities related to the portfolio.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly portfolio_mortgage_balance_min: {
                readonly type: "integer";
                readonly description: "Filter for properties based on the remaining open mortgage balance of the Portfolio for Owners with > 1 property";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly portfolio_mortgage_balance_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly portfolio_purchased_last12_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly portfolio_purchased_last12_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly portfolio_purchased_last6_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly portfolio_purchased_last6_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly portfolio_value_min: {
                readonly type: "integer";
                readonly description: "Filter for properties based on the Total Value of the Portfolio for Owners with > 1 property";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly portfolio_value_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly pre_foreclosure_date_min: {
                readonly type: "string";
                readonly description: "Filter by the Recording Date of Pre-Foreclosure Related Documents. Use with \"pre_foreclosure\": true";
                readonly format: "date";
            };
            readonly pre_foreclosure_date_max: {
                readonly type: "string";
                readonly format: "date";
            };
            readonly prior_owner_months_owned_min: {
                readonly type: "integer";
                readonly description: "Define the time range for what constitutes a \"Flip\" period between the last 2 transactions";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly prior_owner_months_owned_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly properties_owned_min: {
                readonly type: "integer";
                readonly description: "The minimum amount of total properties that any property owner's portfolio will have for each property returned.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly properties_owned_max: {
                readonly type: "integer";
                readonly description: "The maximum amount of total properties that any property owner's portfolio will have for each property returned.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly stories_min: {
                readonly type: "integer";
                readonly description: "The minimum amount of floors/stories you want properties in your response to have";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly stories_max: {
                readonly type: "integer";
                readonly description: "The maximum amount of floors/stories you want properties in your response to have";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly tax_delinquent_year_min: {
                readonly type: "integer";
                readonly description: "2019 - 2022 range yields most results. Matching Min & Max will give a single year range.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly tax_delinquent_year_max: {
                readonly type: "integer";
                readonly description: "2019 - 2022 range yields most results. Matching Min & Max will give a single year range.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly units_min: {
                readonly type: "integer";
                readonly description: "The minimum amount of individual units that the property contains";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly units_max: {
                readonly type: "integer";
                readonly description: "The maximum amount of individual units that the property contains";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly value_min: {
                readonly type: "integer";
                readonly description: "Used for searching a range of properties with an estimated value between a min and max.  Minimum estimated value for the given property search";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly value_max: {
                readonly type: "integer";
                readonly description: "Used for searching a range of properties with an estimated value between a min and max.  Maximum estimated value for the given property search.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly year_min: {
                readonly type: "integer";
                readonly description: "**Deprecation Notice** (replace with year_built_min). Used for searching a range of properties built between a min and max.  Minimum year built for the given property search";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly year_max: {
                readonly type: "integer";
                readonly description: "**Deprecation Notice** (replace with year_built_min). Used for searching a range of properties built between a min and max.  Maximum year built for the given property search";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly year_built_min: {
                readonly type: "integer";
                readonly description: "Used for searching a range of properties built between a min and max.  Minimum year built for the given property search";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly year_built_max: {
                readonly type: "integer";
                readonly description: "Used for searching a range of properties built between a min and max.  Minimum year built for the given property search";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly years_owned_min: {
                readonly type: "integer";
                readonly description: "Number value for lower bound of a range search for years_owned. Used in conjunction with years_owned_max";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly years_owned_max: {
                readonly type: "integer";
                readonly description: "Number value for lower bound of a range search for years_owned. Used in conjunction with years_owned_min.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly last_update_date_min: {
                readonly type: "string";
                readonly description: "fetch property IDs of properties that have been updated in a given time range.";
                readonly format: "date";
            };
            readonly last_update_date_max: {
                readonly type: "string";
                readonly format: "date";
            };
        };
        readonly $schema: "https://json-schema.org/draft/2020-12/schema#";
    };
    readonly metadata: {
        readonly allOf: readonly [{
            readonly type: "object";
            readonly properties: {
                readonly "x-api-key": {
                    readonly type: "string";
                    readonly $schema: "https://json-schema.org/draft/2020-12/schema#";
                    readonly description: "User's API key";
                };
                readonly "x-user-id": {
                    readonly type: "string";
                    readonly $schema: "https://json-schema.org/draft/2020-12/schema#";
                    readonly description: "Denote a unique user identifier to this api call by passing it in this header field";
                };
            };
            readonly required: readonly ["x-api-key"];
        }];
    };
    readonly response: {
        readonly "200": {
            readonly oneOf: readonly [{
                readonly title: "Build a List of Properties";
                readonly type: "object";
                readonly properties: {
                    readonly live: {
                        readonly type: "boolean";
                        readonly default: true;
                        readonly examples: readonly [true];
                    };
                    readonly input: {
                        readonly type: "object";
                        readonly properties: {
                            readonly count: {
                                readonly type: "boolean";
                                readonly default: true;
                                readonly examples: readonly [false];
                            };
                            readonly mls_active: {
                                readonly type: "boolean";
                                readonly default: true;
                                readonly examples: readonly [true];
                            };
                            readonly city: {
                                readonly type: "string";
                                readonly examples: readonly ["Herndon"];
                            };
                            readonly state: {
                                readonly type: "string";
                                readonly examples: readonly ["VA"];
                            };
                        };
                    };
                    readonly data: {
                        readonly type: "array";
                        readonly items: {
                            readonly type: "object";
                            readonly properties: {
                                readonly absenteeOwner: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly address: {
                                    readonly type: "object";
                                    readonly properties: {
                                        readonly address: {
                                            readonly type: "string";
                                            readonly examples: readonly ["13723 Neil Armstrong Ave Unit 308, Herndon, Va 20171"];
                                        };
                                        readonly city: {
                                            readonly type: "string";
                                            readonly examples: readonly ["Herndon"];
                                        };
                                        readonly county: {
                                            readonly type: "string";
                                            readonly examples: readonly ["Fairfax"];
                                        };
                                        readonly state: {
                                            readonly type: "string";
                                            readonly examples: readonly ["VA"];
                                        };
                                        readonly street: {
                                            readonly type: "string";
                                            readonly examples: readonly ["13723 Neil Armstrong Ave Unit 308"];
                                        };
                                        readonly zip: {
                                            readonly type: "string";
                                            readonly examples: readonly ["20171"];
                                        };
                                    };
                                };
                                readonly adjustableRate: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly airConditioningAvailable: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly assessedImprovementValue: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [326590];
                                };
                                readonly assessedLandValue: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [82000];
                                };
                                readonly assessedValue: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [408590];
                                };
                                readonly auction: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly basement: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly bathrooms: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [2];
                                };
                                readonly bedrooms: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [2];
                                };
                                readonly cashBuyer: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly corporateOwned: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly death: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly distressed: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly documentType: {
                                    readonly type: "string";
                                    readonly examples: readonly ["Grant Deed"];
                                };
                                readonly documentTypeCode: {
                                    readonly type: "string";
                                    readonly examples: readonly ["DTGD"];
                                };
                                readonly equity: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly equityPercent: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [38];
                                };
                                readonly estimatedEquity: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [189084];
                                };
                                readonly estimatedValue: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [490308];
                                };
                                readonly floodZone: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [true];
                                };
                                readonly floodZoneDescription: {
                                    readonly type: "string";
                                    readonly examples: readonly ["AREA OF MINIMAL FLOOD HAZARD"];
                                };
                                readonly floodZoneType: {
                                    readonly type: "string";
                                    readonly examples: readonly ["X"];
                                };
                                readonly foreclosure: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly forSale: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly freeClear: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly garage: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly highEquity: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly id: {
                                    readonly type: "string";
                                    readonly examples: readonly ["253175355"];
                                };
                                readonly inherited: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly inStateAbsenteeOwner: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly investorBuyer: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly landUse: {
                                    readonly type: "string";
                                    readonly examples: readonly ["Residential"];
                                };
                                readonly lastMortgage1Amount: {};
                                readonly lastSaleAmount: {
                                    readonly type: "string";
                                    readonly examples: readonly ["418000"];
                                };
                                readonly lastSaleDate: {
                                    readonly type: "string";
                                    readonly examples: readonly ["2020-07-23"];
                                };
                                readonly latitude: {
                                    readonly type: "number";
                                    readonly default: 0;
                                    readonly examples: readonly [38.920743];
                                };
                                readonly lenderName: {
                                    readonly type: "string";
                                    readonly examples: readonly ["One American Mtg"];
                                };
                                readonly listingAmount: {};
                                readonly longitude: {
                                    readonly type: "number";
                                    readonly default: 0;
                                    readonly examples: readonly [-77.421772];
                                };
                                readonly lotSquareFeet: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [0];
                                };
                                readonly mailAddress: {
                                    readonly type: "object";
                                    readonly properties: {
                                        readonly address: {
                                            readonly type: "string";
                                            readonly examples: readonly ["13723 Neil Armstrong Ave Unit 308, Herndon, Va 20171"];
                                        };
                                        readonly city: {
                                            readonly type: "string";
                                            readonly examples: readonly ["Herndon"];
                                        };
                                        readonly county: {
                                            readonly type: "string";
                                            readonly examples: readonly ["Fairfax"];
                                        };
                                        readonly state: {
                                            readonly type: "string";
                                            readonly examples: readonly ["VA"];
                                        };
                                        readonly street: {
                                            readonly type: "string";
                                            readonly examples: readonly ["13723 Neil Armstrong Ave Unit 308"];
                                        };
                                        readonly zip: {
                                            readonly type: "string";
                                            readonly examples: readonly ["20171"];
                                        };
                                    };
                                };
                                readonly medianIncome: {
                                    readonly type: "string";
                                    readonly examples: readonly ["150066"];
                                };
                                readonly MFH2to4: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly MFH5plus: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly mlsActive: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [true];
                                };
                                readonly mlsCancelled: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly mlsDaysOnMarket: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [101];
                                };
                                readonly mlsFailed: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly mlsHasPhotos: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly mlsLastSaleDate: {
                                    readonly type: "string";
                                    readonly examples: readonly ["2020-07-23"];
                                };
                                readonly mlsLastStatusDate: {
                                    readonly type: "string";
                                    readonly examples: readonly ["2023-04-15"];
                                };
                                readonly mlsListingDate: {
                                    readonly type: "string";
                                    readonly examples: readonly ["2023-04-15"];
                                };
                                readonly mlsListingPrice: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [2500];
                                };
                                readonly mlsPending: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly mlsSold: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly mlsStatus: {
                                    readonly type: "string";
                                    readonly examples: readonly ["Active"];
                                };
                                readonly mlsType: {
                                    readonly type: "string";
                                    readonly examples: readonly ["ForSale"];
                                };
                                readonly negativeEquity: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly neighborhood: {
                                    readonly type: "object";
                                    readonly properties: {
                                        readonly center: {
                                            readonly type: "string";
                                            readonly examples: readonly ["POINT(-77.421701188095 38.919564722822)"];
                                        };
                                        readonly id: {
                                            readonly type: "string";
                                            readonly examples: readonly ["63205"];
                                        };
                                        readonly name: {
                                            readonly type: "string";
                                            readonly examples: readonly ["Discovery Square"];
                                        };
                                        readonly type: {
                                            readonly type: "string";
                                            readonly examples: readonly ["subdivision"];
                                        };
                                    };
                                };
                                readonly openMortgageBalance: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [313500];
                                };
                                readonly outOfStateAbsenteeOwner: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly owner1FirstName: {
                                    readonly type: "string";
                                    readonly examples: readonly ["Attili"];
                                };
                                readonly owner1LastName: {
                                    readonly type: "string";
                                    readonly examples: readonly ["Srinivas"];
                                };
                                readonly owner2FirstName: {
                                    readonly type: "string";
                                    readonly examples: readonly ["Attili"];
                                };
                                readonly owner2LastName: {
                                    readonly type: "string";
                                    readonly examples: readonly ["Manjusha"];
                                };
                                readonly ownerOccupied: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [true];
                                };
                                readonly preForeclosure: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly privateLender: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly propertyId: {
                                    readonly type: "string";
                                    readonly examples: readonly ["253175355"];
                                };
                                readonly propertyType: {
                                    readonly type: "string";
                                    readonly examples: readonly ["CONDO"];
                                };
                                readonly propertyUse: {
                                    readonly type: "string";
                                    readonly examples: readonly ["Condominium"];
                                };
                                readonly propertyUseCode: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [366];
                                };
                                readonly rentAmount: {};
                                readonly reo: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly roomsCount: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [5];
                                };
                                readonly squareFeet: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [1323];
                                };
                                readonly suggestedRent: {
                                    readonly type: "string";
                                    readonly examples: readonly ["2070"];
                                };
                                readonly unitsCount: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [0];
                                };
                                readonly vacant: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly yearBuilt: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [2015];
                                };
                                readonly yearsOwned: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [3];
                                };
                            };
                        };
                    };
                    readonly resultCount: {
                        readonly type: "integer";
                        readonly default: 0;
                        readonly examples: readonly [156];
                    };
                    readonly resultIndex: {
                        readonly type: "integer";
                        readonly default: 0;
                        readonly examples: readonly [50];
                    };
                    readonly recordCount: {
                        readonly type: "integer";
                        readonly default: 0;
                        readonly examples: readonly [50];
                    };
                    readonly statusCode: {
                        readonly type: "integer";
                        readonly default: 0;
                        readonly examples: readonly [200];
                    };
                    readonly statusMessage: {
                        readonly type: "string";
                        readonly examples: readonly ["success"];
                    };
                    readonly requestExecutionTimeMS: {
                        readonly type: "string";
                        readonly examples: readonly ["126ms"];
                    };
                };
            }, {
                readonly title: "Property Count Example";
                readonly type: "object";
                readonly properties: {
                    readonly input: {
                        readonly type: "object";
                        readonly properties: {
                            readonly count: {
                                readonly type: "boolean";
                                readonly default: true;
                                readonly examples: readonly [true];
                            };
                            readonly zip: {
                                readonly type: "string";
                                readonly examples: readonly ["08046"];
                            };
                            readonly beds_min: {
                                readonly type: "integer";
                                readonly default: 0;
                                readonly examples: readonly [2];
                            };
                            readonly beds_max: {
                                readonly type: "integer";
                                readonly default: 0;
                                readonly examples: readonly [4];
                            };
                            readonly baths_min: {
                                readonly type: "integer";
                                readonly default: 0;
                                readonly examples: readonly [2];
                            };
                            readonly baths_max: {
                                readonly type: "integer";
                                readonly default: 0;
                                readonly examples: readonly [5];
                            };
                            readonly equity: {
                                readonly type: "integer";
                                readonly default: 0;
                                readonly examples: readonly [250000];
                            };
                            readonly equity_comparison: {
                                readonly type: "string";
                                readonly examples: readonly ["lt"];
                            };
                        };
                    };
                    readonly data: {
                        readonly type: "array";
                        readonly items: {
                            readonly type: "object";
                            readonly properties: {};
                        };
                    };
                };
            }, {
                readonly title: "Obfuscate Properties Example";
                readonly type: "object";
                readonly properties: {
                    readonly input: {
                        readonly type: "object";
                        readonly properties: {
                            readonly count: {
                                readonly type: "boolean";
                                readonly default: true;
                                readonly examples: readonly [false];
                            };
                            readonly obfuscate: {
                                readonly type: "boolean";
                                readonly default: true;
                                readonly examples: readonly [true];
                            };
                            readonly state: {
                                readonly type: "string";
                                readonly examples: readonly ["VA"];
                            };
                            readonly city: {
                                readonly type: "string";
                                readonly examples: readonly ["Arlington"];
                            };
                        };
                    };
                    readonly data: {
                        readonly type: "array";
                        readonly items: {
                            readonly type: "object";
                            readonly properties: {};
                        };
                    };
                };
            }, {
                readonly title: "Property Summary";
                readonly type: "object";
                readonly properties: {
                    readonly input: {
                        readonly type: "object";
                        readonly properties: {
                            readonly summary: {
                                readonly type: "boolean";
                                readonly default: true;
                                readonly examples: readonly [true];
                            };
                            readonly zip: {
                                readonly type: "string";
                                readonly examples: readonly ["08046"];
                            };
                            readonly beds_min: {
                                readonly type: "integer";
                                readonly default: 0;
                                readonly examples: readonly [2];
                            };
                            readonly beds_max: {
                                readonly type: "integer";
                                readonly default: 0;
                                readonly examples: readonly [4];
                            };
                            readonly baths_min: {
                                readonly type: "integer";
                                readonly default: 0;
                                readonly examples: readonly [2];
                            };
                            readonly baths_max: {
                                readonly type: "integer";
                                readonly default: 0;
                                readonly examples: readonly [5];
                            };
                            readonly equity: {
                                readonly type: "integer";
                                readonly default: 0;
                                readonly examples: readonly [250000];
                            };
                            readonly equity_comparison: {
                                readonly type: "string";
                                readonly examples: readonly ["lt"];
                            };
                        };
                    };
                    readonly data: {
                        readonly type: "array";
                        readonly items: {};
                    };
                };
            }];
            readonly $schema: "https://json-schema.org/draft/2020-12/schema#";
        };
    };
};
declare const PropertySearchApiCopy: {
    readonly body: {
        readonly type: "object";
        readonly properties: {
            readonly count: {
                readonly type: "boolean";
                readonly description: "Set to true to only return the count for the total  number of records that would be returned for the search and not the records themselves.";
            };
            readonly ids: {
                readonly type: "array";
                readonly description: "Provide a list of property IDs from past or saved Property Searches to pull back all of the enriched fields";
                readonly items: {
                    readonly type: "integer";
                    readonly format: "int32";
                    readonly minimum: -**********;
                    readonly maximum: **********;
                };
            };
            readonly ids_only: {
                readonly type: "boolean";
                readonly description: "Returns up to 10,000 property IDs matching your search criteria. When provided, the \"size\" and \"resultIndex\" will be ignored.";
                readonly default: false;
            };
            readonly obfuscate: {
                readonly type: "boolean";
                readonly description: "Will remove the address and name fields on the properties returned";
                readonly default: false;
            };
            readonly sort: {
                readonly type: "object";
                readonly description: "Sorts result set based on user defined sorting definitions across the Property Search fields";
                readonly properties: {};
            };
            readonly summary: {
                readonly type: "boolean";
                readonly description: "Returns an aggregation of all lead types in a summary object. The summary object will return totals for each lead type within the context of the given search.";
                readonly default: false;
            };
            readonly resultIndex: {
                readonly type: "integer";
                readonly description: "Used with size to accomplish paging.  The server will skip the number of records specified by resultIndex, and return the records starting after the resultIndex.  The total number of records returned will not be greater than the size specified, or a max of 250 set by the server.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly size: {
                readonly type: "integer";
                readonly description: "Set to the maximum number of records that the server can return for the search.  Used in conjunction with resultIndex for paging results.";
                readonly default: 50;
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly address: {
                readonly type: "string";
                readonly description: "Fully formatted address for a property search.  This should include house, street, city, state and zip";
            };
            readonly house: {
                readonly type: "string";
                readonly description: "Used to search for specific house numbers.   Must be accompanied with state or zip to limit results.";
            };
            readonly street: {
                readonly type: "string";
                readonly description: "Used to search searching street names only.  Must be accompanied with state or zip to limit results.";
            };
            readonly city: {
                readonly type: "string";
                readonly description: "Used to search within a city only.  Must be accompanied with state or zip to limit results.";
            };
            readonly state: {
                readonly type: "string";
                readonly description: "Used to search within a state.  Must be accompanied by city, house, or street to limit results.";
            };
            readonly county: {
                readonly type: "string";
                readonly description: "Used to search within a county.  Must be accompanied by state, or zip.";
            };
            readonly zip: {
                readonly type: "string";
                readonly description: "Used to search within a US zip code. An array of zips (of type:string) can also be provided to this field.";
            };
            readonly latitude: {
                readonly type: "number";
                readonly description: "If latitude & longitude are provided, the search radius will be calculated with that set of coordinates as center";
                readonly format: "float";
                readonly minimum: -3.402823669209385e+38;
                readonly maximum: 3.402823669209385e+38;
            };
            readonly longitude: {
                readonly type: "number";
                readonly description: "If latitude & longitude are provided, the search radius will be calculated with that set of coordinates as center";
                readonly format: "float";
                readonly minimum: -3.402823669209385e+38;
                readonly maximum: 3.402823669209385e+38;
            };
            readonly radius: {
                readonly type: "integer";
                readonly description: "Provide a search radius between 0.1-10 miles for narrowing your search";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly polygon: {
                readonly type: "array";
                readonly description: "Provide an array of latitude/longitude pairs for the Geo portion of your query";
                readonly items: {
                    readonly properties: {
                        readonly lat: {
                            readonly type: "number";
                            readonly format: "double";
                            readonly minimum: -1.7976931348623157e+308;
                            readonly maximum: 1.7976931348623157e+308;
                        };
                        readonly lon: {
                            readonly type: "number";
                            readonly format: "double";
                            readonly minimum: -1.7976931348623157e+308;
                            readonly maximum: 1.7976931348623157e+308;
                        };
                    };
                    readonly type: "object";
                };
            };
            readonly multi_polygon: {
                readonly type: "array";
                readonly description: "Minimum of 1 polygon";
                readonly items: {
                    readonly properties: {
                        readonly boundaries: {
                            readonly type: "array";
                            readonly items: {
                                readonly properties: {
                                    readonly lat: {
                                        readonly type: "number";
                                        readonly format: "double";
                                        readonly minimum: -1.7976931348623157e+308;
                                        readonly maximum: 1.7976931348623157e+308;
                                    };
                                    readonly lon: {
                                        readonly type: "number";
                                        readonly format: "double";
                                        readonly minimum: -1.7976931348623157e+308;
                                        readonly maximum: 1.7976931348623157e+308;
                                    };
                                };
                                readonly type: "object";
                            };
                        };
                    };
                    readonly type: "object";
                };
            };
            readonly property_type: {
                readonly type: "string";
                readonly description: "Provide the type of residences/properties you are looking for";
                readonly enum: readonly ["SFR", "MFR", "LAND", "CONDO", "MOBILE", "OTHER"];
            };
            readonly property_use_code: {
                readonly type: "integer";
                readonly description: "Also accepts an Array of Integers, where each integer is one of our accepted Property Use Codes.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly mls_active: {
                readonly type: "boolean";
                readonly description: "Find active MLS listings";
            };
            readonly mls_pending: {
                readonly type: "boolean";
                readonly description: "Find pending MLS sales that are expected to close";
            };
            readonly mls_cancelled: {
                readonly type: "boolean";
                readonly description: "Find terminated MLS listings";
            };
            readonly mls_days_on_market_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly mls_days_on_market_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly mls_listing_price_min: {
                readonly type: "integer";
                readonly description: "Lower bound used with mls_listing_max to only find properties with MLS listing prices within a defined range";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly mls_listing_price_max: {
                readonly type: "integer";
                readonly description: "Minimum value of 1";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly mls_listing_price: {
                readonly type: "integer";
                readonly description: "The official MLS listing price for the property";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly mls_listing_price_operator: {
                readonly type: "string";
                readonly description: "mls_operator is to be used with mls_listing_price to indicate a range less than or greater than starting with that listing price. For example, { mls_listing_price: 100000, mls_operator: 'gte' } would retrieve all properties with an MLS listing price of $100,000 or more";
                readonly enum: readonly ["lt", "lte", "gt", "gte"];
            };
            readonly id: {
                readonly type: "string";
                readonly description: "AutoComplete Field. Can be a string or an integer. Represents the unique property id in the case of full address autocomplete searches.";
            };
            readonly apn: {
                readonly type: "string";
                readonly description: "AutoComplete Field. The Property's unique tax assessor identifier, returned as part of the AutoComplete API response.";
            };
            readonly stateId: {
                readonly type: "string";
                readonly description: "AutoComplete Field.";
            };
            readonly countyId: {
                readonly type: "string";
                readonly description: "AutoComplete Field.";
            };
            readonly neighborhood_id: {
                readonly type: "integer";
                readonly description: "Autocomplete field.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly neighborhood_name: {
                readonly type: "string";
                readonly description: "Autocomplete field.";
            };
            readonly searchType: {
                readonly type: "string";
                readonly description: "AutoComplete Field. A = full address ; C = city ; N = county; S = street ; Z = zip; G = neighborhood; T = state";
                readonly enum: readonly ["A", "C", "N", "S", "Z", "G", "T"];
            };
            readonly fips: {
                readonly type: "string";
                readonly description: "AutoComplete Field.";
            };
            readonly title: {
                readonly type: "string";
                readonly description: "AutoComplete Field.";
            };
            readonly absentee_owner: {
                readonly type: "boolean";
                readonly description: "Used for searching for properties where the owner is not currently a resident.  Generally signifies a tenant or non-owner occupied property.";
            };
            readonly adjustable_rate: {
                readonly type: "boolean";
                readonly description: "Indicates if the current mortgage on the property has an adjustable rate.";
            };
            readonly assumable: {
                readonly type: "boolean";
                readonly description: "Indicates if the mortgage on a given property is assumable.";
            };
            readonly attic: {
                readonly type: "boolean";
            };
            readonly auction: {
                readonly type: "boolean";
                readonly description: "Used to find properties with an auction date.  Used with search_range or a default max of 1 year.";
            };
            readonly basement: {
                readonly type: "boolean";
                readonly description: "Used to find properties with a basement.";
            };
            readonly breezeway: {
                readonly type: "boolean";
            };
            readonly carport: {
                readonly type: "boolean";
            };
            readonly cash_buyer: {
                readonly type: "boolean";
                readonly description: "Indicates if the property ownership is subsequent to an all cash transaction";
            };
            readonly corporate_owned: {
                readonly type: "boolean";
                readonly description: "Used to find properties where one of the owners is company.";
            };
            readonly death: {
                readonly type: "boolean";
                readonly description: "Used to find properties where the property owner on the deed is recently deceased. Can be used for probate lists.";
            };
            readonly deck: {
                readonly type: "boolean";
                readonly description: "Used to find properties that have a deck";
            };
            readonly equity: {
                readonly type: "boolean";
                readonly description: "Indicates if a property is estimated to have a positive equity amount (>0)";
            };
            readonly feature_balcony: {
                readonly type: "boolean";
            };
            readonly fire_sprinklers: {
                readonly type: "boolean";
            };
            readonly flood_zone: {
                readonly type: "boolean";
                readonly description: "Indicates if the property is in a flood zone area. This flag can be used in conjunction with \"flood_zone_type\" to get more specific result sets.";
            };
            readonly foreclosure: {
                readonly type: "boolean";
                readonly description: "Used to find properties in foreclosure.  Used with search_range or a default max of 1 year.";
            };
            readonly free_clear: {
                readonly type: "boolean";
                readonly description: "Used to find properties without an open mortgage.";
            };
            readonly garage: {
                readonly type: "boolean";
                readonly description: "Used to find properties with a physical structure marked for garage use.";
            };
            readonly high_equity: {
                readonly type: "boolean";
                readonly description: "Indicates properties with high equity (>39%)";
            };
            readonly inherited: {
                readonly type: "boolean";
                readonly description: "Set to true to search inherited properties";
            };
            readonly in_state_owner: {
                readonly type: "boolean";
                readonly description: "Used to find properties with an owner whose mailing address is in the same state as the property address.";
            };
            readonly investor_buyer: {
                readonly type: "boolean";
                readonly description: "Signals that the property was cash purchased by an absentee owner/investor, rather than individual like with the cash_buyer flag";
            };
            readonly judgment: {
                readonly type: "boolean";
                readonly description: "Used to find properties where a lawsuit has been filed against a property owner or a party involved in a real estate transaction, and the court rules in favor of one of the parties, and issued a judgment.";
            };
            readonly mfh_2to4: {
                readonly type: "boolean";
                readonly description: "Multi-family homes with 2 to 4 units";
            };
            readonly mfh_5plus: {
                readonly type: "boolean";
                readonly description: "Multi-family homes with 5 or more units";
            };
            readonly negative_equity: {
                readonly type: "boolean";
                readonly description: "Indicates if a property is estimated to have negative equity";
            };
            readonly out_of_state_owner: {
                readonly type: "boolean";
                readonly description: "Used to find properties with an owner whose mailing address is in a different state as the property address.";
            };
            readonly patio: {
                readonly type: "boolean";
            };
            readonly pool: {
                readonly type: "boolean";
                readonly description: "Used to find properties with a pool";
            };
            readonly pre_foreclosure: {
                readonly type: "boolean";
                readonly description: "Used to find poperties that have received any notice of preforeclosure.   Used with search_range or a default max of 1 year.";
            };
            readonly prior_owner_individual: {
                readonly type: "boolean";
            };
            readonly private_lender: {
                readonly type: "boolean";
                readonly description: "Returns all properties that are currently financed by a private lender";
            };
            readonly quit_claim: {
                readonly type: "boolean";
                readonly description: "Indicates if the property ownership was subsequent to a quit claim";
            };
            readonly reo: {
                readonly type: "boolean";
                readonly description: "Used to find properties owned by a bank, trust, services entity, or tax entity.  Used with search_range or a default max of 1 year.";
            };
            readonly rv_parking: {
                readonly type: "boolean";
            };
            readonly tax_lien: {
                readonly type: "boolean";
                readonly description: "Find properties where there is a tax lien against the property";
            };
            readonly vacant: {
                readonly type: "boolean";
                readonly description: "Used to find properties that are vacant";
            };
            readonly census_block: {
                readonly type: "string";
                readonly description: "Values 1000-5000";
            };
            readonly census_block_group: {
                readonly type: "string";
                readonly description: "Values 0-10";
            };
            readonly census_tract: {
                readonly type: "string";
                readonly description: "Official tract number from the U.S. Census Bureau";
            };
            readonly construction: {
                readonly type: "string";
                readonly description: "Full list of construction types: https://developer.realestateapi.com/reference/construction-types";
            };
            readonly document_type_code: {
                readonly type: "string";
                readonly description: "Used to find a specific document type for more granular searches other than the booleans provided.  This field can also be assigned an array of document type codes. Used in conjunction with search_range, or a maximum default value of 1 year.";
            };
            readonly flood_zone_type: {
                readonly type: "string";
                readonly description: "B, C, X (for moderate to low risk areas); A, AE, A1-30, AH, AO, AR, A99, V, VE, V1 - V30 (High Risk - Coastal Areas); D (Undetermined Risk Zone)";
            };
            readonly loan_type_code_first: {
                readonly type: "string";
            };
            readonly loan_type_code_second: {
                readonly type: "string";
            };
            readonly loan_type_code_third: {
                readonly type: "string";
            };
            readonly notice_type: {
                readonly type: "string";
                readonly description: "Search by the Recording Date of the .foreclosureInfo data for the specified notice type";
                readonly enum: readonly ["FOR", "NOD", "NOL", "NTS", "REO"];
            };
            readonly parcel_account_number: {
                readonly type: "string";
                readonly description: "e.g. 05-00925.01";
            };
            readonly search_range: {
                readonly type: "string";
                readonly description: "Used in conjunction for reo, auction, foreclosure, and preforeclosure searches to limit the search to only return records where the event happened within the provided range.  All ranges work from NOW back to the provided range.";
                readonly enum: readonly ["1_MONTH", "2_MONTH", "3_MONTH", "6_MONTH", "1_YEAR"];
            };
            readonly sewage: {
                readonly type: "string";
                readonly description: "Options: Municipal, Yes, Septic, None, Storm";
            };
            readonly water_source: {
                readonly type: "string";
                readonly description: "Full list of water source types you can filter by: https://developer.realestateapi.com/reference/water-source-searches";
            };
            readonly estimated_equity: {
                readonly type: "integer";
                readonly description: "Used in conjunction with the equity_percent_operator to find properties where the estimated equity amount is greater than or less than the value provided.  Equity dollar amount is computed as the difference of the estimated value less any known open mortgages.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly equity_operator: {
                readonly type: "string";
                readonly description: "Comparison operator for searches using estimated_equity.  Returns properties based on a greater than, or less than operation coupled with the value provided for estimated_equity which is based on total dollars of equity estimated from the estimated value and any known open mortgages.";
                readonly enum: readonly ["lt", "lte", "gt", "gte"];
            };
            readonly equity_percent: {
                readonly type: "integer";
                readonly description: "Used in conjunction with the equity_percent_operator to find properties where the equity percentage is greater than or less than the value provided.  Equity percentage is a based on the difference of the computed LTV.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly equity_percent_operator: {
                readonly type: "string";
                readonly description: "Comparison operator for searches using equity_percent.  Returns properties based on a greater than, or less than operation coupled with the value provided for equity_percent which is based on the difference of the calculated LTV.";
                readonly enum: readonly ["lt", "lte", "gt", "gte"];
            };
            readonly last_sale_date: {
                readonly type: "string";
                readonly description: "Find properties based on the date of the last sale history transaction";
                readonly format: "date";
            };
            readonly last_sale_date_operator: {
                readonly type: "string";
                readonly description: "Used in conjunction with \"last_sale_date\" to find properties that satisfy the range for when they were last sold in a transaction.";
                readonly enum: readonly ["lt", "lte", "gt", "gte"];
            };
            readonly median_income: {
                readonly type: "integer";
                readonly description: "Find properties based on the median income of the Areas that contain the properties";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly median_income_operator: {
                readonly type: "string";
                readonly description: "Used in conjunction with the \"median_income\" field in order to specify the range lower or higher you want to look at from the given median_income.";
                readonly enum: readonly ["lt", "lte", "gt", "gte"];
            };
            readonly years_owned: {
                readonly type: "integer";
                readonly description: "Number value of the years owned you are searching for. To be used with years_owned_operator";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly years_owned_operator: {
                readonly type: "string";
                readonly description: "Operator for less than and greater than searches on years_owned field";
                readonly enum: readonly ["lt", "lte", "gt", "gte"];
            };
            readonly assessed_improvement_value_min: {
                readonly type: "integer";
                readonly description: "Value range search against the county assessed improvement value";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly assessed_improvement_value_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly assessed_land_value_min: {
                readonly type: "integer";
                readonly description: "Value range search against the county assessed land value";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly assessed_land_value_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly assessed_value_min: {
                readonly type: "integer";
                readonly description: "Value range search against the county assessed value";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly assessed_value_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly auction_date_min: {
                readonly type: "string";
                readonly description: "filter on dates of upcoming foreclosure auctions (e.g. Current Date \"2024-05-01\" & set a future date range of \"2024-05-15\" to \"2024-05-30\")";
                readonly format: "date";
            };
            readonly auction_date_max: {
                readonly type: "string";
                readonly format: "date";
            };
            readonly baths_min: {
                readonly type: "integer";
                readonly description: "Used for searching a range of properties with bathrooms between a min and max.  Minimum numbers of bathrooms for the given property search";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly baths_max: {
                readonly type: "integer";
                readonly description: "Used for searching a range of properties with bathrooms between a min and max.  Maximum numbers of bathrooms for the given property search";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly beds_min: {
                readonly type: "integer";
                readonly description: "Used for searching a range of properties with bedrooms between a min and max.  Minimum numbers of bedrooms for the given property search";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly beds_max: {
                readonly type: "integer";
                readonly description: "Used for searching a range of properties with bedrooms between a min and max.  Maximum numbers of bedrooms for the given property search";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly building_size_min: {
                readonly type: "integer";
                readonly description: "Used for searching a range of properties with an interior, living square footage between a min and max.  Minimum square footage of the interior living space for the given property search";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly building_size_max: {
                readonly type: "integer";
                readonly description: "Used for searching a range of properties with an interior, living square footage between a min and max.  Maximum square footage of the interior living space for the given property search";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly deck_area_min: {
                readonly type: "integer";
                readonly description: "In sq. ft.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly deck_area_max: {
                readonly type: "string";
                readonly description: "In sq. ft.";
            };
            readonly estimated_equity_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly estimated_equity_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly last_sale_date_min: {
                readonly type: "string";
                readonly description: "Minimum Date for the last sale transaction date";
                readonly format: "date";
            };
            readonly last_sale_date_max: {
                readonly type: "string";
                readonly description: "Maximum Date for the last sale transaction date";
                readonly format: "date";
            };
            readonly last_sale_price_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly last_sale_price_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly lot_size_min: {
                readonly type: "integer";
                readonly description: "Used for searching a range of properties with lot sizes between a min and max.  Minimum square footage of the exterior lot  built for the given property search";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly lot_size_max: {
                readonly type: "integer";
                readonly description: "Used for searching a range of properties with lot sizes between a min and max.  Maximum square footage of the exterior lot  built for the given property search";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly ltv_min: {
                readonly type: "integer";
                readonly description: "Min. of 0";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly ltv_max: {
                readonly type: "string";
                readonly description: "Max of 100";
            };
            readonly median_income_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly median_income_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly mortgage_min: {
                readonly type: "integer";
                readonly description: "Used for searching a range of properties with an estimated total of open mortgages between a min and max.  Minimum estimated amount for all open mortgages for the given property search.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly mortgage_max: {
                readonly type: "integer";
                readonly description: "Used for searching a range of properties with an estimated total of open mortgages between a min and max.  Maximum estimated amount for all open mortgages for the given property search.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly rooms_min: {
                readonly type: "integer";
                readonly description: "Used for setting the minimum on the number of total rooms you want your properties to have.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly rooms_max: {
                readonly type: "integer";
                readonly description: "Used for setting the maximum on the number of total rooms you want your properties to have.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly pool_area_min: {
                readonly type: "integer";
                readonly description: "In sq. ft.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly pool_area_max: {
                readonly type: "integer";
                readonly description: "In sq. ft.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly portfolio_equity_min: {
                readonly type: "integer";
                readonly description: "Used to find properties where the minimum ownership interest or the stake that an investor has in the portfolio is as specified. Portfolio equity is the difference between the total value of the portfolio and any outstanding debts or liabilities related to the portfolio.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly portfolio_equity_max: {
                readonly type: "integer";
                readonly description: "Used to find properties where the maximum ownership interest or the stake that an investor has in the portfolio is as specified. Portfolio equity is the difference between the total value of the portfolio and any outstanding debts or liabilities related to the portfolio.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly portfolio_mortgage_balance_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly portfolio_mortgage_balance_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly portfolio_purchased_last12_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly portfolio_purchased_last12_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly portfolio_purchased_last6_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly portfolio_purchased_last6_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly portfolio_value_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly portfolio_value_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly pre_foreclosure_date_min: {
                readonly type: "string";
                readonly format: "date";
            };
            readonly pre_foreclosure_date_max: {
                readonly type: "string";
                readonly format: "date";
            };
            readonly prior_owner_months_owned_min: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly prior_owner_months_owned_max: {
                readonly type: "integer";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly properties_owned_min: {
                readonly type: "integer";
                readonly description: "The minimum amount of total properties that any property owner's portfolio will have for each property returned.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly properties_owned_max: {
                readonly type: "integer";
                readonly description: "The maximum amount of total properties that any property owner's portfolio will have for each property returned.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly stories_min: {
                readonly type: "integer";
                readonly description: "The minimum amount of floors/stories you want properties in your response to have";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly stories_max: {
                readonly type: "integer";
                readonly description: "The maximum amount of floors/stories you want properties in your response to have";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly tax_delinquent_year_min: {
                readonly type: "integer";
                readonly description: "2019 - 2022 range yields most results. Matching Min & Max will give a single year range.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly tax_delinquent_year_max: {
                readonly type: "integer";
                readonly description: "2019 - 2022 range yields most results. Matching Min & Max will give a single year range.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly units_min: {
                readonly type: "integer";
                readonly description: "The minimum amount of individual units that the property contains";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly units_max: {
                readonly type: "integer";
                readonly description: "The maximum amount of individual units that the property contains";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly value_min: {
                readonly type: "integer";
                readonly description: "Used for searching a range of properties with an estimated value between a min and max.  Minimum estimated value for the given property search";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly value_max: {
                readonly type: "integer";
                readonly description: "Used for searching a range of properties with an estimated value between a min and max.  Maximum estimated value for the given property search.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly year_min: {
                readonly type: "integer";
                readonly description: "**Deprecation Notice** (replace with year_built_min). Used for searching a range of properties built between a min and max.  Minimum year built for the given property search";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly year_max: {
                readonly type: "integer";
                readonly description: "**Deprecation Notice** (replace with year_built_min). Used for searching a range of properties built between a min and max.  Maximum year built for the given property search";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly year_built_min: {
                readonly type: "integer";
                readonly description: "Used for searching a range of properties built between a min and max.  Minimum year built for the given property search";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly year_built_max: {
                readonly type: "integer";
                readonly description: "Used for searching a range of properties built between a min and max.  Minimum year built for the given property search";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly years_owned_min: {
                readonly type: "integer";
                readonly description: "Number value for lower bound of a range search for years_owned. Used in conjunction with years_owned_max";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly years_owned_max: {
                readonly type: "integer";
                readonly description: "Number value for lower bound of a range search for years_owned. Used in conjunction with years_owned_min.";
                readonly format: "int32";
                readonly minimum: -**********;
                readonly maximum: **********;
            };
            readonly last_update_date_min: {
                readonly type: "string";
                readonly description: "fetch property IDs of properties that have been updated in a given time range.";
                readonly format: "date";
            };
            readonly last_update_date_max: {
                readonly type: "string";
                readonly format: "date";
            };
            readonly foreclosure_date_min: {
                readonly type: "string";
                readonly format: "date";
            };
            readonly foreclosure_date_max: {
                readonly type: "string";
                readonly format: "date";
            };
        };
        readonly $schema: "https://json-schema.org/draft/2020-12/schema#";
    };
    readonly metadata: {
        readonly allOf: readonly [{
            readonly type: "object";
            readonly properties: {
                readonly "x-api-key": {
                    readonly type: "string";
                    readonly $schema: "https://json-schema.org/draft/2020-12/schema#";
                    readonly description: "User's API key";
                };
                readonly "x-user-id": {
                    readonly type: "string";
                    readonly $schema: "https://json-schema.org/draft/2020-12/schema#";
                    readonly description: "Denote a unique user identifier to this api call by passing it in this header field";
                };
            };
            readonly required: readonly ["x-api-key"];
        }];
    };
    readonly response: {
        readonly "200": {
            readonly oneOf: readonly [{
                readonly title: "Build a List of Properties";
                readonly type: "object";
                readonly properties: {
                    readonly live: {
                        readonly type: "boolean";
                        readonly default: true;
                        readonly examples: readonly [true];
                    };
                    readonly input: {
                        readonly type: "object";
                        readonly properties: {
                            readonly count: {
                                readonly type: "boolean";
                                readonly default: true;
                                readonly examples: readonly [false];
                            };
                            readonly mls_active: {
                                readonly type: "boolean";
                                readonly default: true;
                                readonly examples: readonly [true];
                            };
                            readonly city: {
                                readonly type: "string";
                                readonly examples: readonly ["Herndon"];
                            };
                            readonly state: {
                                readonly type: "string";
                                readonly examples: readonly ["VA"];
                            };
                        };
                    };
                    readonly data: {
                        readonly type: "array";
                        readonly items: {
                            readonly type: "object";
                            readonly properties: {
                                readonly absenteeOwner: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly address: {
                                    readonly type: "object";
                                    readonly properties: {
                                        readonly address: {
                                            readonly type: "string";
                                            readonly examples: readonly ["13723 Neil Armstrong Ave Unit 308, Herndon, Va 20171"];
                                        };
                                        readonly city: {
                                            readonly type: "string";
                                            readonly examples: readonly ["Herndon"];
                                        };
                                        readonly county: {
                                            readonly type: "string";
                                            readonly examples: readonly ["Fairfax"];
                                        };
                                        readonly state: {
                                            readonly type: "string";
                                            readonly examples: readonly ["VA"];
                                        };
                                        readonly street: {
                                            readonly type: "string";
                                            readonly examples: readonly ["13723 Neil Armstrong Ave Unit 308"];
                                        };
                                        readonly zip: {
                                            readonly type: "string";
                                            readonly examples: readonly ["20171"];
                                        };
                                    };
                                };
                                readonly adjustableRate: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly airConditioningAvailable: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly assessedImprovementValue: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [326590];
                                };
                                readonly assessedLandValue: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [82000];
                                };
                                readonly assessedValue: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [408590];
                                };
                                readonly auction: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly basement: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly bathrooms: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [2];
                                };
                                readonly bedrooms: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [2];
                                };
                                readonly cashBuyer: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly corporateOwned: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly death: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly distressed: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly documentType: {
                                    readonly type: "string";
                                    readonly examples: readonly ["Grant Deed"];
                                };
                                readonly documentTypeCode: {
                                    readonly type: "string";
                                    readonly examples: readonly ["DTGD"];
                                };
                                readonly equity: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly equityPercent: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [38];
                                };
                                readonly estimatedEquity: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [189084];
                                };
                                readonly estimatedValue: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [490308];
                                };
                                readonly floodZone: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [true];
                                };
                                readonly floodZoneDescription: {
                                    readonly type: "string";
                                    readonly examples: readonly ["AREA OF MINIMAL FLOOD HAZARD"];
                                };
                                readonly floodZoneType: {
                                    readonly type: "string";
                                    readonly examples: readonly ["X"];
                                };
                                readonly foreclosure: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly forSale: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly freeClear: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly garage: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly highEquity: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly id: {
                                    readonly type: "string";
                                    readonly examples: readonly ["253175355"];
                                };
                                readonly inherited: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly inStateAbsenteeOwner: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly investorBuyer: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly landUse: {
                                    readonly type: "string";
                                    readonly examples: readonly ["Residential"];
                                };
                                readonly lastMortgage1Amount: {};
                                readonly lastSaleAmount: {
                                    readonly type: "string";
                                    readonly examples: readonly ["418000"];
                                };
                                readonly lastSaleDate: {
                                    readonly type: "string";
                                    readonly examples: readonly ["2020-07-23"];
                                };
                                readonly latitude: {
                                    readonly type: "number";
                                    readonly default: 0;
                                    readonly examples: readonly [38.920743];
                                };
                                readonly lenderName: {
                                    readonly type: "string";
                                    readonly examples: readonly ["One American Mtg"];
                                };
                                readonly listingAmount: {};
                                readonly longitude: {
                                    readonly type: "number";
                                    readonly default: 0;
                                    readonly examples: readonly [-77.421772];
                                };
                                readonly lotSquareFeet: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [0];
                                };
                                readonly mailAddress: {
                                    readonly type: "object";
                                    readonly properties: {
                                        readonly address: {
                                            readonly type: "string";
                                            readonly examples: readonly ["13723 Neil Armstrong Ave Unit 308, Herndon, Va 20171"];
                                        };
                                        readonly city: {
                                            readonly type: "string";
                                            readonly examples: readonly ["Herndon"];
                                        };
                                        readonly county: {
                                            readonly type: "string";
                                            readonly examples: readonly ["Fairfax"];
                                        };
                                        readonly state: {
                                            readonly type: "string";
                                            readonly examples: readonly ["VA"];
                                        };
                                        readonly street: {
                                            readonly type: "string";
                                            readonly examples: readonly ["13723 Neil Armstrong Ave Unit 308"];
                                        };
                                        readonly zip: {
                                            readonly type: "string";
                                            readonly examples: readonly ["20171"];
                                        };
                                    };
                                };
                                readonly medianIncome: {
                                    readonly type: "string";
                                    readonly examples: readonly ["150066"];
                                };
                                readonly MFH2to4: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly MFH5plus: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly mlsActive: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [true];
                                };
                                readonly mlsCancelled: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly mlsDaysOnMarket: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [101];
                                };
                                readonly mlsFailed: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly mlsHasPhotos: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly mlsLastSaleDate: {
                                    readonly type: "string";
                                    readonly examples: readonly ["2020-07-23"];
                                };
                                readonly mlsLastStatusDate: {
                                    readonly type: "string";
                                    readonly examples: readonly ["2023-04-15"];
                                };
                                readonly mlsListingDate: {
                                    readonly type: "string";
                                    readonly examples: readonly ["2023-04-15"];
                                };
                                readonly mlsListingPrice: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [2500];
                                };
                                readonly mlsPending: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly mlsSold: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly mlsStatus: {
                                    readonly type: "string";
                                    readonly examples: readonly ["Active"];
                                };
                                readonly mlsType: {
                                    readonly type: "string";
                                    readonly examples: readonly ["ForSale"];
                                };
                                readonly negativeEquity: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly neighborhood: {
                                    readonly type: "object";
                                    readonly properties: {
                                        readonly center: {
                                            readonly type: "string";
                                            readonly examples: readonly ["POINT(-77.421701188095 38.919564722822)"];
                                        };
                                        readonly id: {
                                            readonly type: "string";
                                            readonly examples: readonly ["63205"];
                                        };
                                        readonly name: {
                                            readonly type: "string";
                                            readonly examples: readonly ["Discovery Square"];
                                        };
                                        readonly type: {
                                            readonly type: "string";
                                            readonly examples: readonly ["subdivision"];
                                        };
                                    };
                                };
                                readonly openMortgageBalance: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [313500];
                                };
                                readonly outOfStateAbsenteeOwner: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly owner1FirstName: {
                                    readonly type: "string";
                                    readonly examples: readonly ["Attili"];
                                };
                                readonly owner1LastName: {
                                    readonly type: "string";
                                    readonly examples: readonly ["Srinivas"];
                                };
                                readonly owner2FirstName: {
                                    readonly type: "string";
                                    readonly examples: readonly ["Attili"];
                                };
                                readonly owner2LastName: {
                                    readonly type: "string";
                                    readonly examples: readonly ["Manjusha"];
                                };
                                readonly ownerOccupied: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [true];
                                };
                                readonly preForeclosure: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly privateLender: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly propertyId: {
                                    readonly type: "string";
                                    readonly examples: readonly ["253175355"];
                                };
                                readonly propertyType: {
                                    readonly type: "string";
                                    readonly examples: readonly ["CONDO"];
                                };
                                readonly propertyUse: {
                                    readonly type: "string";
                                    readonly examples: readonly ["Condominium"];
                                };
                                readonly propertyUseCode: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [366];
                                };
                                readonly rentAmount: {};
                                readonly reo: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly roomsCount: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [5];
                                };
                                readonly squareFeet: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [1323];
                                };
                                readonly suggestedRent: {
                                    readonly type: "string";
                                    readonly examples: readonly ["2070"];
                                };
                                readonly unitsCount: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [0];
                                };
                                readonly vacant: {
                                    readonly type: "boolean";
                                    readonly default: true;
                                    readonly examples: readonly [false];
                                };
                                readonly yearBuilt: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [2015];
                                };
                                readonly yearsOwned: {
                                    readonly type: "integer";
                                    readonly default: 0;
                                    readonly examples: readonly [3];
                                };
                            };
                        };
                    };
                    readonly resultCount: {
                        readonly type: "integer";
                        readonly default: 0;
                        readonly examples: readonly [156];
                    };
                    readonly resultIndex: {
                        readonly type: "integer";
                        readonly default: 0;
                        readonly examples: readonly [50];
                    };
                    readonly recordCount: {
                        readonly type: "integer";
                        readonly default: 0;
                        readonly examples: readonly [50];
                    };
                    readonly statusCode: {
                        readonly type: "integer";
                        readonly default: 0;
                        readonly examples: readonly [200];
                    };
                    readonly statusMessage: {
                        readonly type: "string";
                        readonly examples: readonly ["success"];
                    };
                    readonly requestExecutionTimeMS: {
                        readonly type: "string";
                        readonly examples: readonly ["126ms"];
                    };
                };
            }, {
                readonly title: "Property Count Example";
                readonly type: "object";
                readonly properties: {
                    readonly input: {
                        readonly type: "object";
                        readonly properties: {
                            readonly count: {
                                readonly type: "boolean";
                                readonly default: true;
                                readonly examples: readonly [true];
                            };
                            readonly zip: {
                                readonly type: "string";
                                readonly examples: readonly ["08046"];
                            };
                            readonly beds_min: {
                                readonly type: "integer";
                                readonly default: 0;
                                readonly examples: readonly [2];
                            };
                            readonly beds_max: {
                                readonly type: "integer";
                                readonly default: 0;
                                readonly examples: readonly [4];
                            };
                            readonly baths_min: {
                                readonly type: "integer";
                                readonly default: 0;
                                readonly examples: readonly [2];
                            };
                            readonly baths_max: {
                                readonly type: "integer";
                                readonly default: 0;
                                readonly examples: readonly [5];
                            };
                            readonly equity: {
                                readonly type: "integer";
                                readonly default: 0;
                                readonly examples: readonly [250000];
                            };
                            readonly equity_comparison: {
                                readonly type: "string";
                                readonly examples: readonly ["lt"];
                            };
                        };
                    };
                    readonly data: {
                        readonly type: "array";
                        readonly items: {
                            readonly type: "object";
                            readonly properties: {};
                        };
                    };
                };
            }, {
                readonly title: "Obfuscate Properties Example";
                readonly type: "object";
                readonly properties: {
                    readonly input: {
                        readonly type: "object";
                        readonly properties: {
                            readonly count: {
                                readonly type: "boolean";
                                readonly default: true;
                                readonly examples: readonly [false];
                            };
                            readonly obfuscate: {
                                readonly type: "boolean";
                                readonly default: true;
                                readonly examples: readonly [true];
                            };
                            readonly state: {
                                readonly type: "string";
                                readonly examples: readonly ["VA"];
                            };
                            readonly city: {
                                readonly type: "string";
                                readonly examples: readonly ["Arlington"];
                            };
                        };
                    };
                    readonly data: {
                        readonly type: "array";
                        readonly items: {
                            readonly type: "object";
                            readonly properties: {};
                        };
                    };
                };
            }, {
                readonly title: "Property Summary";
                readonly type: "object";
                readonly properties: {
                    readonly input: {
                        readonly type: "object";
                        readonly properties: {
                            readonly summary: {
                                readonly type: "boolean";
                                readonly default: true;
                                readonly examples: readonly [true];
                            };
                            readonly zip: {
                                readonly type: "string";
                                readonly examples: readonly ["08046"];
                            };
                            readonly beds_min: {
                                readonly type: "integer";
                                readonly default: 0;
                                readonly examples: readonly [2];
                            };
                            readonly beds_max: {
                                readonly type: "integer";
                                readonly default: 0;
                                readonly examples: readonly [4];
                            };
                            readonly baths_min: {
                                readonly type: "integer";
                                readonly default: 0;
                                readonly examples: readonly [2];
                            };
                            readonly baths_max: {
                                readonly type: "integer";
                                readonly default: 0;
                                readonly examples: readonly [5];
                            };
                            readonly equity: {
                                readonly type: "integer";
                                readonly default: 0;
                                readonly examples: readonly [250000];
                            };
                            readonly equity_comparison: {
                                readonly type: "string";
                                readonly examples: readonly ["lt"];
                            };
                        };
                    };
                    readonly data: {
                        readonly type: "array";
                        readonly items: {};
                    };
                };
            }];
            readonly $schema: "https://json-schema.org/draft/2020-12/schema#";
        };
    };
};
export { CsvGeneratorApi, InvoluntaryLienApi, PropertyDetailApi1, PropertyDetailBulkApi, PropertySearchApi, PropertySearchApiCopy };
