import type * as types from './types';
import type { ConfigOptions, FetchResponse } from 'api/dist/core';
import Oas from 'oas';
import APICore from 'api/dist/core';
declare class SDK {
    spec: Oas;
    core: APICore;
    constructor();
    /**
     * Optionally configure various options that the SDK allows.
     *
     * @param config Object of supported SDK options and toggles.
     * @param config.timeout Override the default `fetch` request timeout of 30 seconds. This number
     * should be represented in milliseconds.
     */
    config(config: ConfigOptions): void;
    /**
     * If the API you're using requires authentication you can supply the required credentials
     * through this method and the library will magically determine how they should be used
     * within your API request.
     *
     * With the exception of OpenID and MutualTLS, it supports all forms of authentication
     * supported by the OpenAPI specification.
     *
     * @example <caption>HTTP Basic auth</caption>
     * sdk.auth('username', 'password');
     *
     * @example <caption>Bearer tokens (HTTP or OAuth 2)</caption>
     * sdk.auth('myBearerToken');
     *
     * @example <caption>API Keys</caption>
     * sdk.auth('myApiKey');
     *
     * @see {@link https://spec.openapis.org/oas/v3.0.3#fixed-fields-22}
     * @see {@link https://spec.openapis.org/oas/v3.1.0#fixed-fields-22}
     * @param values Your auth credentials for the API; can specify up to two strings or numbers.
     */
    auth(...values: string[] | number[]): this;
    /**
     * If the API you're using offers alternate server URLs, and server variables, you can tell
     * the SDK which one to use with this method. To use it you can supply either one of the
     * server URLs that are contained within the OpenAPI definition (along with any server
     * variables), or you can pass it a fully qualified URL to use (that may or may not exist
     * within the OpenAPI definition).
     *
     * @example <caption>Server URL with server variables</caption>
     * sdk.server('https://{region}.api.example.com/{basePath}', {
     *   name: 'eu',
     *   basePath: 'v14',
     * });
     *
     * @example <caption>Fully qualified server URL</caption>
     * sdk.server('https://eu.api.example.com/v14');
     *
     * @param url Server URL
     * @param variables An object of variables to replace into the server URL.
     */
    server(url: string, variables?: {}): void;
    /**
     * Comps, Mortgages, Mailing Addresses, Property Sales History & More!
     *
     * @summary Property Detail API
     * @throws FetchError<400, types.PropertyDetailApi1Response400> 400
     * @throws FetchError<401, types.PropertyDetailApi1Response401> 401
     * @throws FetchError<404, types.PropertyDetailApi1Response404> 404
     * @throws FetchError<429, types.PropertyDetailApi1Response429> 429
     * @throws FetchError<500, types.PropertyDetailApi1Response500> 500
     */
    propertyDetailApi1(body?: types.PropertyDetailApi1BodyParam, metadata?: types.PropertyDetailApi1MetadataParam): Promise<FetchResponse<200, types.PropertyDetailApi1Response200>>;
    /**
     * For retrieving of up to 1000 properties at once.  Can be used standalone, but it's
     * designed to work together with the Property Search API.  Use this API for quickly
     * exporting lists, or bulk search requests for analytics.  Pass in addresses, or a list of
     * ID's returned from the Property Search API.
     *
     * @summary Property Detail Bulk API
     */
    propertyDetailBulkApi(body: types.PropertyDetailBulkApiBodyParam, metadata?: types.PropertyDetailBulkApiMetadataParam): Promise<FetchResponse<200, types.PropertyDetailBulkApiResponse200>>;
    /**
     * Searchable API for list building, search counts, and advanced filtering on properties.
     * You can also use this API to implement your own comparables API, or property analytics
     * API.  Questions?  Contact our team to ask us for best practices with using this API.This
     * API implements easy paging so your apps can easily manage filtered results in a results
     * pane with paging.  When your user clicks on a result, just use the id from this API to
     * get the full property results using the Property Detail API.  Questions on best
     * practices for implementing paged property results in your app?  Just contact our team.
     *
     * @summary Property Search API
     */
    propertySearchApi(body: types.PropertySearchApiBodyParam, metadata: types.PropertySearchApiMetadataParam): Promise<FetchResponse<200, types.PropertySearchApiResponse200>>;
    propertySearchApi(metadata: types.PropertySearchApiMetadataParam): Promise<FetchResponse<200, types.PropertySearchApiResponse200>>;
    /**
     * Searchable API for list building, search counts, and advanced filtering on properties.
     * You can also use this API to implement your own comparables API, or property analytics
     * API.  Questions?  Contact our team to ask us for best practices with using this API.This
     * API implements easy paging so your apps can easily manage filtered results in a results
     * pane with paging.  When your user clicks on a result, just use the id from this API to
     * get the full property results using the Property Detail API.  Questions on best
     * practices for implementing paged property results in your app?  Just contact our team.
     *
     * @summary Property Search API (COPY)
     */
    propertySearchApiCopy(body: types.PropertySearchApiCopyBodyParam, metadata: types.PropertySearchApiCopyMetadataParam): Promise<FetchResponse<200, types.PropertySearchApiCopyResponse200>>;
    propertySearchApiCopy(metadata: types.PropertySearchApiCopyMetadataParam): Promise<FetchResponse<200, types.PropertySearchApiCopyResponse200>>;
    /**
     * CSV Generator API
     *
     * @throws FetchError<400, types.CsvGeneratorApiResponse400> 400
     */
    csvGeneratorApi(body?: types.CsvGeneratorApiBodyParam): Promise<FetchResponse<200, types.CsvGeneratorApiResponse200>>;
    /**
     * Go beyond our standard tax_liens & add Involuntary Lien Data to your Insights on a
     * Property
     *
     * @summary Involuntary Liens API
     * @throws FetchError<400, types.InvoluntaryLienApiResponse400> 400
     */
    involuntaryLienApi(body?: types.InvoluntaryLienApiBodyParam): Promise<FetchResponse<200, types.InvoluntaryLienApiResponse200>>;
}
declare const createSDK: SDK;
export = createSDK;
