import type { FromSchema } from 'json-schema-to-ts';
import * as schemas from './schemas';
export type CsvGeneratorApiBodyParam = FromSchema<typeof schemas.CsvGeneratorApi.body>;
export type CsvGeneratorApiResponse200 = FromSchema<typeof schemas.CsvGeneratorApi.response['200']>;
export type CsvGeneratorApiResponse400 = FromSchema<typeof schemas.CsvGeneratorApi.response['400']>;
export type InvoluntaryLienApiBodyParam = FromSchema<typeof schemas.InvoluntaryLienApi.body>;
export type InvoluntaryLienApiResponse200 = FromSchema<typeof schemas.InvoluntaryLienApi.response['200']>;
export type InvoluntaryLienApiResponse400 = FromSchema<typeof schemas.InvoluntaryLienApi.response['400']>;
export type PropertyDetailApi1BodyParam = FromSchema<typeof schemas.PropertyDetailApi1.body>;
export type PropertyDetailApi1MetadataParam = FromSchema<typeof schemas.PropertyDetailApi1.metadata>;
export type PropertyDetailApi1Response200 = FromSchema<typeof schemas.PropertyDetailApi1.response['200']>;
export type PropertyDetailApi1Response400 = FromSchema<typeof schemas.PropertyDetailApi1.response['400']>;
export type PropertyDetailApi1Response401 = FromSchema<typeof schemas.PropertyDetailApi1.response['401']>;
export type PropertyDetailApi1Response404 = FromSchema<typeof schemas.PropertyDetailApi1.response['404']>;
export type PropertyDetailApi1Response429 = FromSchema<typeof schemas.PropertyDetailApi1.response['429']>;
export type PropertyDetailApi1Response500 = FromSchema<typeof schemas.PropertyDetailApi1.response['500']>;
export type PropertyDetailBulkApiBodyParam = FromSchema<typeof schemas.PropertyDetailBulkApi.body>;
export type PropertyDetailBulkApiMetadataParam = FromSchema<typeof schemas.PropertyDetailBulkApi.metadata>;
export type PropertyDetailBulkApiResponse200 = FromSchema<typeof schemas.PropertyDetailBulkApi.response['200']>;
export type PropertySearchApiBodyParam = FromSchema<typeof schemas.PropertySearchApi.body>;
export type PropertySearchApiCopyBodyParam = FromSchema<typeof schemas.PropertySearchApiCopy.body>;
export type PropertySearchApiCopyMetadataParam = FromSchema<typeof schemas.PropertySearchApiCopy.metadata>;
export type PropertySearchApiCopyResponse200 = FromSchema<typeof schemas.PropertySearchApiCopy.response['200']>;
export type PropertySearchApiMetadataParam = FromSchema<typeof schemas.PropertySearchApi.metadata>;
export type PropertySearchApiResponse200 = FromSchema<typeof schemas.PropertySearchApi.response['200']>;
