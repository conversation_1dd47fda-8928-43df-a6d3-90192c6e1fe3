"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var oas_1 = __importDefault(require("oas"));
var core_1 = __importDefault(require("api/dist/core"));
var openapi_json_1 = __importDefault(require("./openapi.json"));
var SDK = /** @class */ (function () {
    function SDK() {
        this.spec = oas_1.default.init(openapi_json_1.default);
        this.core = new core_1.default(this.spec, 'realestateapi/unknown (api/6.1.3)');
    }
    /**
     * Optionally configure various options that the SDK allows.
     *
     * @param config Object of supported SDK options and toggles.
     * @param config.timeout Override the default `fetch` request timeout of 30 seconds. This number
     * should be represented in milliseconds.
     */
    SDK.prototype.config = function (config) {
        this.core.setConfig(config);
    };
    /**
     * If the API you're using requires authentication you can supply the required credentials
     * through this method and the library will magically determine how they should be used
     * within your API request.
     *
     * With the exception of OpenID and MutualTLS, it supports all forms of authentication
     * supported by the OpenAPI specification.
     *
     * @example <caption>HTTP Basic auth</caption>
     * sdk.auth('username', 'password');
     *
     * @example <caption>Bearer tokens (HTTP or OAuth 2)</caption>
     * sdk.auth('myBearerToken');
     *
     * @example <caption>API Keys</caption>
     * sdk.auth('myApiKey');
     *
     * @see {@link https://spec.openapis.org/oas/v3.0.3#fixed-fields-22}
     * @see {@link https://spec.openapis.org/oas/v3.1.0#fixed-fields-22}
     * @param values Your auth credentials for the API; can specify up to two strings or numbers.
     */
    SDK.prototype.auth = function () {
        var _a;
        var values = [];
        for (var _i = 0; _i < arguments.length; _i++) {
            values[_i] = arguments[_i];
        }
        (_a = this.core).setAuth.apply(_a, values);
        return this;
    };
    /**
     * If the API you're using offers alternate server URLs, and server variables, you can tell
     * the SDK which one to use with this method. To use it you can supply either one of the
     * server URLs that are contained within the OpenAPI definition (along with any server
     * variables), or you can pass it a fully qualified URL to use (that may or may not exist
     * within the OpenAPI definition).
     *
     * @example <caption>Server URL with server variables</caption>
     * sdk.server('https://{region}.api.example.com/{basePath}', {
     *   name: 'eu',
     *   basePath: 'v14',
     * });
     *
     * @example <caption>Fully qualified server URL</caption>
     * sdk.server('https://eu.api.example.com/v14');
     *
     * @param url Server URL
     * @param variables An object of variables to replace into the server URL.
     */
    SDK.prototype.server = function (url, variables) {
        if (variables === void 0) { variables = {}; }
        this.core.setServer(url, variables);
    };
    /**
     * Comps, Mortgages, Mailing Addresses, Property Sales History & More!
     *
     * @summary Property Detail API
     * @throws FetchError<400, types.PropertyDetailApi1Response400> 400
     * @throws FetchError<401, types.PropertyDetailApi1Response401> 401
     * @throws FetchError<404, types.PropertyDetailApi1Response404> 404
     * @throws FetchError<429, types.PropertyDetailApi1Response429> 429
     * @throws FetchError<500, types.PropertyDetailApi1Response500> 500
     */
    SDK.prototype.propertyDetailApi1 = function (body, metadata) {
        return this.core.fetch('/v2/PropertyDetail', 'post', body, metadata);
    };
    /**
     * For retrieving of up to 1000 properties at once.  Can be used standalone, but it's
     * designed to work together with the Property Search API.  Use this API for quickly
     * exporting lists, or bulk search requests for analytics.  Pass in addresses, or a list of
     * ID's returned from the Property Search API.
     *
     * @summary Property Detail Bulk API
     */
    SDK.prototype.propertyDetailBulkApi = function (body, metadata) {
        return this.core.fetch('/v2/PropertyDetailBulk', 'post', body, metadata);
    };
    SDK.prototype.propertySearchApi = function (body, metadata) {
        return this.core.fetch('/v2/PropertySearch', 'post', body, metadata);
    };
    SDK.prototype.propertySearchApiCopy = function (body, metadata) {
        return this.core.fetch('/v2/PropertySearch (COPY)', 'post', body, metadata);
    };
    /**
     * CSV Generator API
     *
     * @throws FetchError<400, types.CsvGeneratorApiResponse400> 400
     */
    SDK.prototype.csvGeneratorApi = function (body) {
        return this.core.fetch('/v2/CSVBuilder', 'post', body);
    };
    /**
     * Go beyond our standard tax_liens & add Involuntary Lien Data to your Insights on a
     * Property
     *
     * @summary Involuntary Liens API
     * @throws FetchError<400, types.InvoluntaryLienApiResponse400> 400
     */
    SDK.prototype.involuntaryLienApi = function (body) {
        return this.core.fetch('/v2/Reports/PropertyLiens', 'post', body);
    };
    return SDK;
}());
var createSDK = (function () { return new SDK(); })();
module.exports = createSDK;
