-- Migration script to add API key management tables to PropBolt database
-- Run this script to add API key functionality to existing database

-- Create API Keys table
CREATE TABLE IF NOT EXISTS api_keys (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    key_name VARCHAR(255) NOT NULL,
    api_key VARCHAR(255) UNIQUE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    rate_limit INTEGER DEFAULT 1000,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used_at TIMESTAMP,
    expires_at TIMESTAMP
);

-- Create API Usage tracking table
CREATE TABLE IF NOT EXISTS api_usage (
    id SERIAL PRIMARY KEY,
    api_key_id INTEGER REFERENCES api_keys(id) ON DELETE CASCADE,
    endpoint VARCHAR(255) NOT NULL,
    request_count INTEGER DEFAULT 1,
    date DATE DEFAULT CURRENT_DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(api_key_id, endpoint, date)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_api_keys_user ON api_keys(user_id);
CREATE INDEX IF NOT EXISTS idx_api_keys_key ON api_keys(api_key);
CREATE INDEX IF NOT EXISTS idx_api_keys_active ON api_keys(is_active);
CREATE INDEX IF NOT EXISTS idx_api_usage_key_date ON api_usage(api_key_id, date);
CREATE INDEX IF NOT EXISTS idx_api_usage_endpoint ON api_usage(endpoint);

-- Add comments for documentation
COMMENT ON TABLE api_keys IS 'Stores API keys for user authentication to data endpoints';
COMMENT ON TABLE api_usage IS 'Tracks API usage statistics for rate limiting and analytics';

COMMENT ON COLUMN api_keys.key_name IS 'User-friendly name for the API key';
COMMENT ON COLUMN api_keys.api_key IS 'The actual API key (64-character hex string)';
COMMENT ON COLUMN api_keys.is_active IS 'Whether the API key is currently active';
COMMENT ON COLUMN api_keys.rate_limit IS 'Daily request limit for this API key';
COMMENT ON COLUMN api_keys.last_used_at IS 'Timestamp of last API request using this key';
COMMENT ON COLUMN api_keys.expires_at IS 'Optional expiration date for the API key';

COMMENT ON COLUMN api_usage.endpoint IS 'The API endpoint that was accessed';
COMMENT ON COLUMN api_usage.request_count IS 'Number of requests to this endpoint on this date';
COMMENT ON COLUMN api_usage.date IS 'Date of the API usage (for daily aggregation)';

-- Insert sample API key for testing (optional - remove in production)
-- This creates a test API key for user ID 1 if it exists
-- INSERT INTO api_keys (user_id, key_name, api_key, rate_limit)
-- SELECT 1, 'Test API Key', 'test_api_key_12345678901234567890123456789012', 1000
-- WHERE EXISTS (SELECT 1 FROM users WHERE id = 1)
-- ON CONFLICT (api_key) DO NOTHING;

-- Verify the migration
SELECT 'API Keys table created successfully' as status
WHERE EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_name = 'api_keys'
);

SELECT 'API Usage table created successfully' as status
WHERE EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_name = 'api_usage'
);
