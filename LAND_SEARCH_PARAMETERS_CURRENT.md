# PropBolt Land Search Parameters - Current Implementation

## API Being Used
**RealEstateAPI.com** - Professional real estate data API
- **Base URL**: `https://api.realestateapi.com`
- **Endpoint**: `/v2/PropertySearch` (POST request)
- **Authentication**: API Key via `X-API-KEY` header
- **Environment Variable**: `REAL_ESTATE_API_KEY`

## Currently Implemented Parameters

### Basic Search Parameters (Currently Used)
```go
// Location parameters
City         string  // e.g., "Daytona Beach"
State        string  // e.g., "FL"
County       string  // e.g., "Volusia"
Zip          string  // e.g., "32114"

// Property type
PropertyType string  // Default: "LAND"

// Property condition
Vacant       bool    // Default: true for land search

// Financial filters
ValueMin     int     // Minimum property value
ValueMax     int     // Maximum property value

// Lot size filters
LotSizeMin   int     // Minimum lot size in square feet
LotSizeMax   int     // Maximum lot size in square feet

// Pagination
Size         int     // Default: 100 results
```

### API Endpoint Usage
```
GET /api/v1/data/search?city=Daytona Beach&state=FL&property_type=LAND&vacant=true&min_price=50000&max_price=200000&min_lot_size=5000&max_lot_size=50000
```

## Available But Not Currently Used Parameters

### Location Parameters
```go
Address      string  // Full address search
Latitude     float64 // GPS coordinates
Longitude    float64 // GPS coordinates  
Radius       float64 // Search radius in miles
```

### Property Details
```go
BedsMin         int // Minimum bedrooms
BedsMax         int // Maximum bedrooms
BathsMin        int // Minimum bathrooms
BathsMax        int // Maximum bathrooms
BuildingSizeMin int // Minimum building size
BuildingSizeMax int // Maximum building size
YearBuiltMin    int // Minimum year built
YearBuiltMax    int // Maximum year built
UnitsMin        int // Minimum units
UnitsMax        int // Maximum units
```

### Financial Filters
```go
EquityMin     int    // Minimum equity
EquityMax     int    // Maximum equity
SalePriceMin  int    // Minimum sale price
SalePriceMax  int    // Maximum sale price
SaleDateMin   string // Minimum sale date
SaleDateMax   string // Maximum sale date
```

### MLS Parameters
```go
MLSActive             bool // Active MLS listings
MLSPending            bool // Pending MLS listings
MLSSold               bool // Sold MLS listings
MLSDaysOnMarketMin    int  // Minimum days on market
MLSDaysOnMarketMax    int  // Maximum days on market
```

### Ownership Filters
```go
AbsenteeOwner        bool // Absentee owner properties
CorporateOwned       bool // Corporate owned properties
TrustOwned           bool // Trust owned properties
LLCOwned             bool // LLC owned properties
OwnerOccupied        bool // Owner occupied properties
OwnershipLengthMin   int  // Minimum ownership length
OwnershipLengthMax   int  // Maximum ownership length
```

### Property Condition
```go
Distressed     bool   // Distressed properties
Foreclosure    bool   // Foreclosure properties
PreForeclosure bool   // Pre-foreclosure properties
Auction        bool   // Auction properties
TaxLien        bool   // Tax lien properties
CodeViolation  bool   // Code violation properties
FloodZone      string // Flood zone designation
```

### Investment Filters
```go
HighEquity      bool // High equity properties
NegativeEquity  bool // Negative equity properties
FreeClear       bool // Free and clear properties
CashBuyer       bool // Cash buyer properties
AssumableLoan   bool // Assumable loan properties
REO             bool // REO properties
QuitClaim       bool // Quit claim properties
FlippedMinTimes int  // Minimum times flipped
FlippedWithinYears int // Flipped within years
```

### Construction/Features
```go
ConstructionType string // Construction type
HeatingType      string // Heating type
CoolingType      string // Cooling type
Pool             bool   // Has pool
Garage           bool   // Has garage
Basement         bool   // Has basement
Waterfront       bool   // Waterfront property
```

### Multi-family
```go
MFH2to4 bool // Multi-family 2-4 units
```

### Special Modes
```go
Count    bool     // Return count only
Summary  bool     // Return summary statistics
IDsOnly  bool     // Return IDs only
Exclude  []string // Exclude specific fields
```

### Pagination and Sorting
```go
Sort        map[string]string // Sort parameters
ResultIndex int               // Starting index for pagination
Size        int               // Number of results per page
UserID      string            // User ID for tracking
```

## Response Data Structure

### PropertySearchResult
```go
ID           string  // Property ID
PropertyID   string  // Alternative property ID
Address      string  // Full address
City         string  // City name
State        string  // State abbreviation
ZipCode      string  // ZIP code
Latitude     float64 // GPS latitude
Longitude    float64 // GPS longitude
PropertyType string  // Property type
Bedrooms     int     // Number of bedrooms
Bathrooms    float64 // Number of bathrooms
SquareFeet   int     // Square footage
LotSize      int     // Lot size in square feet
YearBuilt    int     // Year built
Value        int     // Estimated value
Equity       int     // Estimated equity
MLSActive    bool    // Active on MLS
MLSPrice     int     // MLS listing price
Vacant       bool    // Is vacant
Distressed   bool    // Is distressed
```

## Recommendations for Enhanced Land Search

### High Priority Additions
1. **Zoning Information** - Add zoning type filters
2. **Land Use Codes** - Specific land use classifications
3. **Utilities Available** - Water, sewer, electric availability
4. **Road Access** - Paved vs unpaved road access
5. **Topography** - Flat, sloped, waterfront designation

### Medium Priority Additions
1. **Environmental Factors** - Wetlands, protected areas
2. **Development Potential** - Subdivision potential
3. **Agricultural Use** - Farm/agricultural classification
4. **Mineral Rights** - Mineral rights included/excluded

### Integration Opportunities
1. **Chain Lease Potential** - Custom scoring algorithm
2. **Proximity Analysis** - Distance to amenities
3. **Market Trends** - Price appreciation data
4. **Investment Scoring** - ROI potential calculation

## Current API Endpoints

### Land Search Endpoints
- `GET /api/v1/data/search` - General property search
- `GET /api/v1/data/search/sold` - Sold properties with history
- `GET /api/v1/data/search/rentals` - Rental potential properties
- `GET /api/v1/data/property/details` - Detailed property information
- `GET /api/v1/data/autocomplete` - Address autocomplete

### Authentication Required
All data endpoints require API key authentication via:
```
Authorization: Bearer YOUR_API_KEY
```

## Example API Calls

### Basic Land Search
```bash
curl -X GET "https://propbolt.com/api/v1/data/search?city=Daytona Beach&state=FL&property_type=LAND&vacant=true&min_price=50000&max_price=200000" \
  -H "Authorization: Bearer YOUR_API_KEY"
```

### Advanced Land Search with Lot Size
```bash
curl -X GET "https://propbolt.com/api/v1/data/search?city=Daytona Beach&state=FL&property_type=LAND&vacant=true&min_lot_size=10000&max_lot_size=87120&absentee_owner=true" \
  -H "Authorization: Bearer YOUR_API_KEY"
```

### Property Details
```bash
curl -X GET "https://propbolt.com/api/v1/data/property/details?address=123 Main St, Daytona Beach, FL" \
  -H "Authorization: Bearer YOUR_API_KEY"
```
