#!/bin/bash

# PropBolt API Key Management Deployment Script
# This script deploys the API key management functionality to Google Cloud

set -e  # Exit on any error

echo "🚀 Starting PropBolt API Key Management Deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID="gold-braid-458901-v2"
REGION="us-central1"
DATABASE_INSTANCE="propbolt-postgres"

echo -e "${BLUE}📋 Deployment Configuration:${NC}"
echo "  Project ID: $PROJECT_ID"
echo "  Region: $REGION"
echo "  Database Instance: $DATABASE_INSTANCE"
echo ""

# Check if gcloud is installed and authenticated
if ! command -v gcloud &> /dev/null; then
    echo -e "${RED}❌ Error: gcloud CLI is not installed${NC}"
    echo "Please install gcloud CLI: https://cloud.google.com/sdk/docs/install"
    exit 1
fi

# Check if user is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo -e "${RED}❌ Error: Not authenticated with gcloud${NC}"
    echo "Please run: gcloud auth login"
    exit 1
fi

# Set the project
echo -e "${YELLOW}🔧 Setting up Google Cloud project...${NC}"
gcloud config set project $PROJECT_ID

# Check if database instance exists
echo -e "${YELLOW}🔍 Checking database instance...${NC}"
if ! gcloud sql instances describe $DATABASE_INSTANCE --quiet &> /dev/null; then
    echo -e "${RED}❌ Error: Database instance '$DATABASE_INSTANCE' not found${NC}"
    echo "Please ensure the database instance exists and is running"
    exit 1
fi

echo -e "${GREEN}✅ Database instance found${NC}"

# Run database migration
echo -e "${YELLOW}📊 Running database migration...${NC}"
if [ -f "database/migrate_api_keys.sql" ]; then
    echo "Executing API key tables migration..."
    gcloud sql connect $DATABASE_INSTANCE --user=postgres --quiet < database/migrate_api_keys.sql
    echo -e "${GREEN}✅ Database migration completed${NC}"
else
    echo -e "${RED}❌ Error: Migration file not found: database/migrate_api_keys.sql${NC}"
    exit 1
fi

# Build the Go application
echo -e "${YELLOW}🔨 Building Go application...${NC}"
echo "Running go mod tidy..."
go mod tidy

echo "Building for Linux/AMD64..."
GOOS=linux GOARCH=amd64 go build -o propbolt .

if [ ! -f "propbolt" ]; then
    echo -e "${RED}❌ Error: Failed to build application${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Application built successfully${NC}"

# Deploy to Google App Engine
echo -e "${YELLOW}🚀 Deploying to Google App Engine...${NC}"

# Check if app.yaml exists
if [ ! -f "app.yaml" ]; then
    echo -e "${RED}❌ Error: app.yaml not found${NC}"
    echo "Please ensure app.yaml exists in the project root"
    exit 1
fi

# Deploy the application
echo "Deploying backend service..."
gcloud app deploy app.yaml --quiet --promote

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Backend deployment successful${NC}"
else
    echo -e "${RED}❌ Error: Backend deployment failed${NC}"
    exit 1
fi

# Get the deployed URL
APP_URL=$(gcloud app browse --no-launch-browser 2>/dev/null | grep -o 'https://[^[:space:]]*')
if [ -z "$APP_URL" ]; then
    APP_URL="https://$PROJECT_ID.uc.r.appspot.com"
fi

echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"
echo ""
echo -e "${BLUE}📋 Deployment Summary:${NC}"
echo "  ✅ Database migration completed"
echo "  ✅ Go application built and deployed"
echo "  ✅ API key management endpoints available"
echo ""
echo -e "${BLUE}🔗 Application URLs:${NC}"
echo "  Backend API: $APP_URL"
echo "  Health Check: $APP_URL/health"
echo "  API Documentation: See API_DOCUMENTATION.md"
echo ""
echo -e "${BLUE}🔑 API Key Management Endpoints:${NC}"
echo "  List Keys: GET $APP_URL/api/v1/user/api-keys"
echo "  Create Key: POST $APP_URL/api/v1/user/api-keys/create"
echo "  Delete Key: DELETE $APP_URL/api/v1/user/api-keys/delete"
echo "  Usage Stats: GET $APP_URL/api/v1/user/api-keys/usage"
echo "  Key Stats: GET $APP_URL/api/v1/user/api-keys/stats"
echo ""
echo -e "${BLUE}📊 Data API Endpoints (Require API Key):${NC}"
echo "  Property Details: GET $APP_URL/api/v1/data/property"
echo "  Property Search: GET $APP_URL/api/v1/data/search"
echo "  Sold Properties: GET $APP_URL/api/v1/data/search/sold"
echo "  Rental Properties: GET $APP_URL/api/v1/data/search/rentals"
echo "  Property Details (Enhanced): GET $APP_URL/api/v1/data/property/details"
echo "  AutoComplete: GET $APP_URL/api/v1/data/autocomplete"
echo "  Health Check: GET $APP_URL/api/v1/data/health"
echo ""
echo -e "${YELLOW}⚠️  Next Steps:${NC}"
echo "  1. Test the API endpoints using the documentation"
echo "  2. Create API keys for data account users"
echo "  3. Monitor usage and rate limits"
echo "  4. Set up monitoring and alerting"
echo ""
echo -e "${GREEN}✨ PropBolt API Key Management is now live!${NC}"
