#!/bin/bash

# PropBolt API Key Management Testing Script
# This script tests the API key management functionality

set -e

echo "🧪 Testing PropBolt API Key Management..."

# Configuration
BASE_URL="https://propbolt.com"
if [ "$1" = "local" ]; then
    BASE_URL="http://localhost:8080"
    echo "Testing against local server: $BASE_URL"
else
    echo "Testing against production: $BASE_URL"
fi

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Test counter
TESTS_PASSED=0
TESTS_FAILED=0

# Function to run a test
run_test() {
    local test_name="$1"
    local command="$2"
    local expected_status="$3"
    
    echo -e "${BLUE}🔍 Testing: $test_name${NC}"
    
    # Run the command and capture both status and output
    set +e
    response=$(eval "$command" 2>&1)
    status=$?
    set -e
    
    if [ "$status" -eq "$expected_status" ]; then
        echo -e "${GREEN}✅ PASS: $test_name${NC}"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        if [ "$expected_status" -eq 0 ]; then
            echo "Response: $response" | head -3
        fi
    else
        echo -e "${RED}❌ FAIL: $test_name${NC}"
        echo "Expected status: $expected_status, Got: $status"
        echo "Response: $response"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    echo ""
}

# Test 1: Health Check
run_test "Health Check" \
    "curl -s -f $BASE_URL/health" \
    0

# Test 2: API Key Management Endpoints (without auth - should fail)
run_test "List API Keys (No Auth)" \
    "curl -s -f $BASE_URL/api/v1/user/api-keys" \
    22  # curl exit code for HTTP error

# Test 3: Data API Endpoints (without API key - should fail)
run_test "Property Search (No API Key)" \
    "curl -s -f $BASE_URL/api/v1/data/search" \
    22  # curl exit code for HTTP error

# Test 4: Data API Health Check (without API key - should fail)
run_test "Data API Health Check (No API Key)" \
    "curl -s -f $BASE_URL/api/v1/data/health" \
    22  # curl exit code for HTTP error

# Test 5: Invalid API Key
run_test "Property Search (Invalid API Key)" \
    "curl -s -f -H 'Authorization: Bearer invalid_key' $BASE_URL/api/v1/data/search" \
    22  # curl exit code for HTTP error

# Test 6: Check if database migration endpoints exist
run_test "API Key Creation Endpoint Exists" \
    "curl -s -o /dev/null -w '%{http_code}' $BASE_URL/api/v1/user/api-keys/create" \
    0

# Test 7: Test CORS headers
run_test "CORS Headers Check" \
    "curl -s -I $BASE_URL/health | grep -i 'access-control-allow-origin'" \
    0

# Test 8: Test comprehensive search parameters
echo -e "${BLUE}🔍 Testing: Comprehensive Search Parameters${NC}"
response=$(curl -s -f -H "Authorization: Bearer invalid_key" "$BASE_URL/api/v1/data/search?city=Daytona%20Beach&state=FL&property_type=LAND&vacant=true&value_min=50000&value_max=200000&lot_size_min=10000&absentee_owner=true&mls_active=true" 2>&1)
if echo "$response" | grep -q "401\|Unauthorized"; then
    echo -e "${GREEN}✅ PASS: Comprehensive search properly requires authentication${NC}"
    TESTS_PASSED=$((TESTS_PASSED + 1))
else
    echo -e "${RED}❌ FAIL: Comprehensive search authentication not working${NC}"
    TESTS_FAILED=$((TESTS_FAILED + 1))
fi
echo ""

# Test 9: Test rate limiting headers (with invalid key)
echo -e "${BLUE}🔍 Testing: Rate Limiting Headers${NC}"
response=$(curl -s -I -H "Authorization: Bearer invalid_key" $BASE_URL/api/v1/data/search 2>&1)
if echo "$response" | grep -q "401\|Unauthorized"; then
    echo -e "${GREEN}✅ PASS: Rate limiting properly rejects invalid keys${NC}"
    TESTS_PASSED=$((TESTS_PASSED + 1))
else
    echo -e "${RED}❌ FAIL: Rate limiting not working properly${NC}"
    TESTS_FAILED=$((TESTS_FAILED + 1))
fi
echo ""

# Test 10: Check if all required endpoints are accessible
endpoints=(
    "/api/v1/user/api-keys"
    "/api/v1/user/api-keys/create"
    "/api/v1/user/api-keys/delete"
    "/api/v1/user/api-keys/usage"
    "/api/v1/user/api-keys/stats"
    "/api/v1/data/property"
    "/api/v1/data/search"
    "/api/v1/data/search/sold"
    "/api/v1/data/search/rentals"
    "/api/v1/data/property/details"
    "/api/v1/data/autocomplete"
    "/api/v1/data/health"
)

echo -e "${BLUE}🔍 Testing: All Required Endpoints Exist${NC}"
missing_endpoints=0
for endpoint in "${endpoints[@]}"; do
    status_code=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL$endpoint" || echo "000")
    if [ "$status_code" = "000" ] || [ "$status_code" = "404" ]; then
        echo -e "${RED}❌ Missing endpoint: $endpoint${NC}"
        missing_endpoints=$((missing_endpoints + 1))
    else
        echo -e "${GREEN}✅ Endpoint exists: $endpoint (HTTP $status_code)${NC}"
    fi
done

if [ "$missing_endpoints" -eq 0 ]; then
    echo -e "${GREEN}✅ PASS: All endpoints exist${NC}"
    TESTS_PASSED=$((TESTS_PASSED + 1))
else
    echo -e "${RED}❌ FAIL: $missing_endpoints endpoints missing${NC}"
    TESTS_FAILED=$((TESTS_FAILED + 1))
fi
echo ""

# Test Summary
echo -e "${BLUE}📊 Test Summary:${NC}"
echo -e "  ${GREEN}✅ Tests Passed: $TESTS_PASSED${NC}"
echo -e "  ${RED}❌ Tests Failed: $TESTS_FAILED${NC}"
echo ""

if [ "$TESTS_FAILED" -eq 0 ]; then
    echo -e "${GREEN}🎉 All tests passed! API Key Management is working correctly.${NC}"
    exit 0
else
    echo -e "${RED}⚠️  Some tests failed. Please check the implementation.${NC}"
    exit 1
fi
