# PropBolt Complete Search Parameters Guide

## 🔍 **Comprehensive Property Search API**

**Endpoint**: `GET /api/v1/data/search`

This endpoint now supports **ALL** available RealEstateAPI parameters for maximum search flexibility.

## 📍 **Location Parameters**

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `city` | string | City name | `Daytona Beach` |
| `state` | string | State abbreviation | `FL` |
| `county` | string | County name | `Volusia` |
| `zip` | string | ZIP code | `32114` |
| `address` | string | Full address | `123 Main St, Daytona Beach, FL` |
| `latitude` | float | GPS latitude | `29.2108` |
| `longitude` | float | GPS longitude | `-81.0228` |
| `radius` | float | Search radius in miles | `5.0` |

## 🏠 **Property Details**

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `property_type` | string | Property type (default: "LAND") | `LAND`, `RESIDENTIAL`, `COMMERCIAL` |
| `beds_min` | int | Minimum bedrooms | `2` |
| `beds_max` | int | Maximum bedrooms | `5` |
| `baths_min` | int | Minimum bathrooms | `1` |
| `baths_max` | int | Maximum bathrooms | `3` |
| `building_size_min` | int | Minimum building size (sq ft) | `1000` |
| `building_size_max` | int | Maximum building size (sq ft) | `5000` |
| `lot_size_min` | int | Minimum lot size (sq ft) | `5000` |
| `lot_size_max` | int | Maximum lot size (sq ft) | `87120` |
| `year_built_min` | int | Minimum year built | `1990` |
| `year_built_max` | int | Maximum year built | `2023` |
| `units_min` | int | Minimum units | `1` |
| `units_max` | int | Maximum units | `4` |

## 💰 **Financial Filters**

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `value_min` | int | Minimum property value | `50000` |
| `value_max` | int | Maximum property value | `500000` |
| `equity_min` | int | Minimum equity | `25000` |
| `equity_max` | int | Maximum equity | `200000` |
| `sale_price_min` | int | Minimum sale price | `40000` |
| `sale_price_max` | int | Maximum sale price | `300000` |
| `sale_date_min` | string | Minimum sale date | `2020-01-01` |
| `sale_date_max` | string | Maximum sale date | `2023-12-31` |

## 🏢 **MLS Parameters**

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `mls_active` | bool | Active MLS listings (FOR SALE) | `true` |
| `mls_pending` | bool | Pending MLS listings | `true` |
| `mls_sold` | bool | Sold MLS listings | `true` |
| `mls_days_on_market_min` | int | Minimum days on market | `30` |
| `mls_days_on_market_max` | int | Maximum days on market | `365` |

**Note**: If no MLS parameters are specified, defaults to `mls_active=true` (FOR SALE only).

## 👥 **Ownership Filters**

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `absentee_owner` | bool | Absentee owner properties | `true` |
| `corporate_owned` | bool | Corporate owned properties | `true` |
| `trust_owned` | bool | Trust owned properties | `true` |
| `llc_owned` | bool | LLC owned properties | `true` |
| `owner_occupied` | bool | Owner occupied properties | `true` |
| `ownership_length_min` | int | Minimum ownership length (years) | `5` |
| `ownership_length_max` | int | Maximum ownership length (years) | `20` |

## 🏚️ **Property Condition**

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `vacant` | bool | Vacant properties | `true` |
| `distressed` | bool | Distressed properties | `true` |
| `foreclosure` | bool | Foreclosure properties | `true` |
| `pre_foreclosure` | bool | Pre-foreclosure properties | `true` |
| `auction` | bool | Auction properties | `true` |
| `tax_lien` | bool | Tax lien properties | `true` |
| `code_violation` | bool | Code violation properties | `true` |
| `flood_zone` | string | Flood zone designation | `A`, `X`, `VE` |

## 💼 **Investment Filters**

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `high_equity` | bool | High equity properties | `true` |
| `negative_equity` | bool | Negative equity properties | `true` |
| `free_clear` | bool | Free and clear properties | `true` |
| `cash_buyer` | bool | Cash buyer properties | `true` |
| `assumable_loan` | bool | Assumable loan properties | `true` |
| `reo` | bool | REO properties | `true` |
| `quit_claim` | bool | Quit claim properties | `true` |
| `flipped_min_times` | int | Minimum times flipped | `1` |
| `flipped_within_years` | int | Flipped within years | `2` |

## 🏗️ **Construction/Features**

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `construction_type` | string | Construction type | `Frame`, `Masonry`, `Steel` |
| `heating_type` | string | Heating type | `Gas`, `Electric`, `Oil` |
| `cooling_type` | string | Cooling type | `Central`, `Window`, `None` |
| `pool` | bool | Has pool | `true` |
| `garage` | bool | Has garage | `true` |
| `basement` | bool | Has basement | `true` |
| `waterfront` | bool | Waterfront property | `true` |

## 🏘️ **Multi-Family**

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `mfh_2to4` | bool | Multi-family 2-4 units | `true` |

## ⚙️ **Special Modes**

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `count` | bool | Return count only | `true` |
| `summary` | bool | Return summary statistics | `true` |
| `ids_only` | bool | Return IDs only | `true` |
| `exclude` | string | Exclude specific fields (comma-separated) | `saleHistory,taxInfo` |

## 📄 **Pagination and Sorting**

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `result_index` | int | Starting index for pagination | `0` |
| `size` | int | Number of results per page (default: 100) | `50` |
| `user_id` | string | User ID for tracking | `user123` |
| `sort` | string | Sort parameters (field:direction) | `value:desc,lotSize:asc` |

## 🎯 **Example API Calls**

### Basic Land Search (FOR SALE)
```bash
curl -X GET "https://propbolt.com/api/v1/data/search?city=Daytona Beach&state=FL&property_type=LAND&vacant=true" \
  -H "Authorization: Bearer YOUR_API_KEY"
```

### Advanced Land Search with Multiple Filters
```bash
curl -X GET "https://propbolt.com/api/v1/data/search?city=Daytona Beach&state=FL&property_type=LAND&vacant=true&value_min=50000&value_max=200000&lot_size_min=10000&absentee_owner=true&high_equity=true&mls_active=true" \
  -H "Authorization: Bearer YOUR_API_KEY"
```

### Investment-Focused Search
```bash
curl -X GET "https://propbolt.com/api/v1/data/search?state=FL&property_type=LAND&distressed=true&foreclosure=true&high_equity=true&cash_buyer=true&mls_active=true" \
  -H "Authorization: Bearer YOUR_API_KEY"
```

### Waterfront Land Search
```bash
curl -X GET "https://propbolt.com/api/v1/data/search?city=Daytona Beach&state=FL&property_type=LAND&waterfront=true&vacant=true&value_min=100000&mls_active=true" \
  -H "Authorization: Bearer YOUR_API_KEY"
```

### Large Lot Search with Sorting
```bash
curl -X GET "https://propbolt.com/api/v1/data/search?state=FL&property_type=LAND&lot_size_min=43560&vacant=true&sort=lotSize:desc,value:asc&size=25&mls_active=true" \
  -H "Authorization: Bearer YOUR_API_KEY"
```

## 🔒 **Authentication**

All requests require API key authentication:
```
Authorization: Bearer YOUR_API_KEY
```

## 📊 **Response Format**

```json
{
  "success": true,
  "data": [
    {
      "id": "property_id",
      "address": "123 Main St",
      "city": "Daytona Beach",
      "state": "FL",
      "zipCode": "32114",
      "latitude": 29.2108,
      "longitude": -81.0228,
      "propertyType": "LAND",
      "value": 150000,
      "equity": 75000,
      "lotSize": 21780,
      "vacant": true,
      "mlsActive": true,
      "absenteeOwner": true,
      "waterfront": false
    }
  ],
  "count": 25,
  "pagination": {
    "currentPage": 1,
    "totalPages": 5,
    "pageSize": 25,
    "totalResults": 125,
    "hasMore": true
  }
}
```

## 🎯 **Default Behavior**

- **Property Type**: Defaults to `LAND` if not specified
- **MLS Status**: Defaults to `mls_active=true` (FOR SALE) if no MLS parameters specified
- **Page Size**: Defaults to 100 results if not specified
- **Sorting**: No default sorting unless specified

This comprehensive search API gives you complete control over finding the exact land properties that match your investment criteria!
